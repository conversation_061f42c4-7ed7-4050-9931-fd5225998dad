package main

import (
	"fmt"
	"os"
	"path/filepath"
	"time"
)

// PerformanceDemo manages interactive performance demonstrations
type PerformanceDemo struct {
	csvFile    string
	outputBase string
	tableName  string
}

// NewPerformanceDemo creates a new performance demo
func NewPerformanceDemo(csvFile, outputBase, tableName string) *PerformanceDemo {
	return &PerformanceDemo{
		csvFile:    csvFile,
		outputBase: outputBase,
		tableName:  tableName,
	}
}

// RunInteractiveDemo runs an interactive performance demonstration
func (d *PerformanceDemo) RunInteractiveDemo() error {
	fmt.Println("🎭 ULTRAFAST INDEXING PERFORMANCE DEMO")
	fmt.Println("="*80)
	fmt.Printf("📊 Dataset: %s\n", d.csvFile)
	fmt.Printf("📂 Output: %s\n", d.outputBase)
	fmt.Printf("🏷️  Table: %s\n\n", d.tableName)

	for {
		fmt.Println("Choose a demo option:")
		fmt.Println("1. 🔧 Generate All Indexes")
		fmt.Println("2. 🔍 Query Performance Comparison")
		fmt.Println("3. 🏁 Full Benchmark Suite")
		fmt.Println("4. 📊 Storage Analysis")
		fmt.Println("5. 🎯 Interactive Query Testing")
		fmt.Println("6. 📈 Performance Charts")
		fmt.Println("7. 🚪 Exit")
		fmt.Print("\nEnter your choice (1-7): ")

		var choice int
		fmt.Scanf("%d", &choice)

		switch choice {
		case 1:
			d.runGenerationDemo()
		case 2:
			d.runQueryDemo()
		case 3:
			d.runFullBenchmark()
		case 4:
			d.runStorageAnalysis()
		case 5:
			d.runInteractiveQuery()
		case 6:
			d.showPerformanceCharts()
		case 7:
			fmt.Println("👋 Thanks for using UltraFast Indexing Demo!")
			return nil
		default:
			fmt.Println("❌ Invalid choice. Please try again.")
		}

		fmt.Println("\nPress Enter to continue...")
		fmt.Scanln()
		fmt.Scanln() // Handle the newline
	}
}

// runGenerationDemo demonstrates index generation
func (d *PerformanceDemo) runGenerationDemo() {
	fmt.Println("\n🔧 INDEX GENERATION DEMO")
	fmt.Println("="*60)

	result, err := GenerateUnifiedIndexes(d.csvFile, d.outputBase, d.tableName)
	if err != nil {
		fmt.Printf("❌ Generation failed: %v\n", err)
		return
	}

	result.PrintSummary()
	d.showGenerationInsights(result)
}

// runQueryDemo demonstrates query performance
func (d *PerformanceDemo) runQueryDemo() {
	fmt.Println("\n🔍 QUERY PERFORMANCE DEMO")
	fmt.Println("="*60)

	queryEngine := NewUnifiedQueryEngine(d.outputBase)
	if err := queryEngine.Initialize(); err != nil {
		fmt.Printf("❌ Failed to initialize query engines: %v\n", err)
		return
	}
	defer queryEngine.Close()

	// Run sample queries
	sampleQueries := []struct {
		column   string
		operator string
		value    interface{}
		desc     string
	}{
		{"protocol", "=", "TCP", "High-frequency protocol query"},
		{"action", "=", "ALLOW", "Common action filter"},
		{"rule_name", "=", "firewall_rule_1", "Specific rule lookup"},
		{"source_username", "=", "john_doe", "User-specific query"},
	}

	for i, query := range sampleQueries {
		fmt.Printf("\n📋 Query %d: %s\n", i+1, query.desc)
		fmt.Printf("🔍 %s %s %v\n", query.column, query.operator, query.value)

		result, err := queryEngine.ExecuteUnifiedQuery(query.column, query.operator, query.value)
		if err != nil {
			fmt.Printf("❌ Query failed: %v\n", err)
			continue
		}

		result.PrintSummary()
		d.showQueryInsights(result)
	}
}

// runFullBenchmark runs the comprehensive benchmark
func (d *PerformanceDemo) runFullBenchmark() {
	fmt.Println("\n🏁 COMPREHENSIVE BENCHMARK DEMO")
	fmt.Println("="*60)

	result, err := RunComprehensiveBenchmark(d.csvFile, d.outputBase, d.tableName)
	if err != nil {
		fmt.Printf("❌ Benchmark failed: %v\n", err)
		return
	}

	result.PrintComprehensiveReport()

	// Save report
	reportFile := filepath.Join(d.outputBase, fmt.Sprintf("benchmark_report_%d.txt", time.Now().Unix()))
	if err := result.SaveReport(reportFile); err != nil {
		fmt.Printf("⚠️  Failed to save report: %v\n", err)
	}
}

// runStorageAnalysis analyzes storage efficiency
func (d *PerformanceDemo) runStorageAnalysis() {
	fmt.Println("\n💾 STORAGE ANALYSIS DEMO")
	fmt.Println("="*60)

	// First ensure indexes are generated
	result, err := GenerateUnifiedIndexes(d.csvFile, d.outputBase, d.tableName)
	if err != nil {
		fmt.Printf("❌ Failed to generate indexes: %v\n", err)
		return
	}

	d.showDetailedStorageAnalysis(result)
}

// runInteractiveQuery allows user to input custom queries
func (d *PerformanceDemo) runInteractiveQuery() {
	fmt.Println("\n🎯 INTERACTIVE QUERY DEMO")
	fmt.Println("="*60)

	queryEngine := NewUnifiedQueryEngine(d.outputBase)
	if err := queryEngine.Initialize(); err != nil {
		fmt.Printf("❌ Failed to initialize query engines: %v\n", err)
		return
	}
	defer queryEngine.Close()

	fmt.Println("Available columns: protocol, action, rule_name, source_username, destination_port")
	fmt.Println("Supported operators: = (equality)")
	fmt.Println("Type 'quit' to exit interactive mode\n")

	for {
		var column, operator, value string
		
		fmt.Print("Enter column name: ")
		fmt.Scanf("%s", &column)
		if column == "quit" {
			break
		}

		fmt.Print("Enter operator (=): ")
		fmt.Scanf("%s", &operator)
		if operator == "quit" {
			break
		}

		fmt.Print("Enter value: ")
		fmt.Scanf("%s", &value)
		if value == "quit" {
			break
		}

		result, err := queryEngine.ExecuteUnifiedQuery(column, operator, value)
		if err != nil {
			fmt.Printf("❌ Query failed: %v\n", err)
			continue
		}

		result.PrintSummary()
		fmt.Println()
	}
}

// showPerformanceCharts displays ASCII performance charts
func (d *PerformanceDemo) showPerformanceCharts() {
	fmt.Println("\n📈 PERFORMANCE CHARTS")
	fmt.Println("="*60)

	// Run a quick benchmark to get data
	result, err := RunComprehensiveBenchmark(d.csvFile, d.outputBase, d.tableName)
	if err != nil {
		fmt.Printf("❌ Failed to generate chart data: %v\n", err)
		return
	}

	d.drawPerformanceCharts(result)
}

// showGenerationInsights provides insights about generation performance
func (d *PerformanceDemo) showGenerationInsights(result *UnifiedGenerationResult) {
	fmt.Println("\n💡 GENERATION INSIGHTS")
	fmt.Println("-"*40)

	successful := result.GetSuccessfulImplementations()
	if len(successful) == 0 {
		fmt.Println("❌ No successful implementations to analyze")
		return
	}

	// Find fastest and slowest
	var fastest, slowest string
	var fastestTime, slowestTime time.Duration = time.Hour, 0

	for name, impl := range successful {
		if impl.BuildTime < fastestTime {
			fastestTime = impl.BuildTime
			fastest = name
		}
		if impl.BuildTime > slowestTime {
			slowestTime = impl.BuildTime
			slowest = name
		}
	}

	speedup := float64(slowestTime) / float64(fastestTime)
	fmt.Printf("🚀 %s is %.1fx faster than %s\n", fastest, speedup, slowest)

	// Size comparison
	var smallest, largest string
	var smallestSize, largestSize int64 = 1<<63-1, 0

	for name, impl := range successful {
		if impl.TotalSize < smallestSize {
			smallestSize = impl.TotalSize
			smallest = name
		}
		if impl.TotalSize > largestSize {
			largestSize = impl.TotalSize
			largest = name
		}
	}

	sizeRatio := float64(largestSize) / float64(smallestSize)
	fmt.Printf("💾 %s uses %.1fx less storage than %s\n", smallest, sizeRatio, largest)
}

// showQueryInsights provides insights about query performance
func (d *PerformanceDemo) showQueryInsights(result *UnifiedQueryResult) {
	fmt.Println("\n💡 QUERY INSIGHTS")
	fmt.Println("-"*40)

	var fastest, slowest string
	var fastestTime, slowestTime time.Duration = time.Hour, 0

	for name, engineResult := range result.Results {
		if !engineResult.Success {
			continue
		}

		if engineResult.ExecutionTime < fastestTime {
			fastestTime = engineResult.ExecutionTime
			fastest = name
		}
		if engineResult.ExecutionTime > slowestTime {
			slowestTime = engineResult.ExecutionTime
			slowest = name
		}
	}

	if fastest != "" && slowest != "" {
		speedup := float64(slowestTime) / float64(fastestTime)
		fmt.Printf("⚡ %s is %.1fx faster than %s\n", fastest, speedup, slowest)
		
		if fastestTime < time.Millisecond {
			fmt.Printf("🎯 %s achieved sub-millisecond performance (%v)\n", fastest, fastestTime.Round(time.Microsecond))
		}
	}
}

// showDetailedStorageAnalysis shows detailed storage analysis
func (d *PerformanceDemo) showDetailedStorageAnalysis(result *UnifiedGenerationResult) {
	fmt.Println("Storage Breakdown by Implementation:")
	fmt.Println("-"*60)

	for name, impl := range result.Implementations {
		if !impl.Success {
			continue
		}

		fmt.Printf("\n📦 %s:\n", name)
		fmt.Printf("  Total Size: %s\n", formatBytes(impl.TotalSize))
		fmt.Printf("  Records: %d\n", impl.TotalRecords)
		fmt.Printf("  Size per Record: %.2f bytes\n", float64(impl.TotalSize)/float64(impl.TotalRecords))
		fmt.Printf("  Features: %v\n", impl.Features)

		if len(impl.ColumnStats) > 0 {
			fmt.Printf("  Column Breakdown:\n")
			for colName, stats := range impl.ColumnStats {
				fmt.Printf("    %s: %s (%d records)\n", 
					colName, formatBytes(stats.FileSize), stats.RecordCount)
			}
		}
	}
}

// drawPerformanceCharts draws ASCII performance charts
func (d *PerformanceDemo) drawPerformanceCharts(result *FullBenchmarkResult) {
	fmt.Println("Query Performance Chart (Average Time):")
	fmt.Println("-"*60)

	// Find max time for scaling
	var maxTime time.Duration
	for _, engine := range result.QueryResult.EngineResults {
		if engine.SuccessfulQueries > 0 && engine.AverageTime > maxTime {
			maxTime = engine.AverageTime
		}
	}

	if maxTime == 0 {
		fmt.Println("❌ No successful queries to chart")
		return
	}

	// Draw bars
	for name, engine := range result.QueryResult.EngineResults {
		if engine.SuccessfulQueries == 0 {
			continue
		}

		barLength := int(float64(engine.AverageTime) / float64(maxTime) * 40)
		bar := ""
		for i := 0; i < barLength; i++ {
			bar += "█"
		}

		fmt.Printf("%-20s |%s %v\n", name, bar, engine.AverageTime.Round(time.Microsecond))
	}

	fmt.Println("\nStorage Efficiency Chart:")
	fmt.Println("-"*60)

	// Find max size for scaling
	var maxSize int64
	for _, impl := range result.GenerationResult.Implementations {
		if impl.Success && impl.TotalSize > maxSize {
			maxSize = impl.TotalSize
		}
	}

	// Draw storage bars
	for name, impl := range result.GenerationResult.Implementations {
		if !impl.Success {
			continue
		}

		barLength := int(float64(impl.TotalSize) / float64(maxSize) * 40)
		bar := ""
		for i := 0; i < barLength; i++ {
			bar += "█"
		}

		fmt.Printf("%-20s |%s %s\n", name, bar, formatBytes(impl.TotalSize))
	}
}

// RunPerformanceDemo is a convenience function to run the interactive demo
func RunPerformanceDemo(csvFile, outputBase, tableName string) error {
	demo := NewPerformanceDemo(csvFile, outputBase, tableName)
	return demo.RunInteractiveDemo()
}
