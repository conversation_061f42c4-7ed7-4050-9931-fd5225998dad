package main

import (
	"time"
)

// IndexingApproach represents different indexing strategies
type IndexingApproach string

const (
	ApproachExisting    IndexingApproach = "existing_binary"
	ApproachExistingInx IndexingApproach = "existing_compressed"
	ApproachV2          IndexingApproach = "ultrafast_v2"
	ApproachV3          IndexingApproach = "ultrafast_v3"
	ApproachBTree       IndexingApproach = "btree"
	ApproachLSMTree     IndexingApproach = "lsm_tree"
	ApproachInverted    IndexingApproach = "inverted_index"
	ApproachBitmap      IndexingApproach = "bitmap_index"
)

// Note: Record and RowRecord types are already defined in main.go
// We'll use the existing types to avoid redeclaration

// QueryFilter represents a query filter condition
type QueryFilter struct {
	Column   string
	Operator string // "=", "!=", ">", "<", ">=", "<=", "IN", "LIKE"
	Value    interface{}
	Values   []interface{} // For IN operator
}

// QueryResult represents the result of a query operation
type QueryResult struct {
	LineNumbers     []uint32
	ExecutionTime   time.Duration
	ResultCount     int
	IndexHits       int
	BloomFilterHits int
	MemoryUsed      int64
	DiskReads       int
}

// QueryRowResult represents the result of a query operation with complete rows
type QueryRowResult struct {
	Rows          []map[string]interface{} // Complete rows with selected columns
	LineNumbers   []uint32                 // Line numbers that match the query
	ExecutionTime time.Duration            // Time taken to execute the query and fetch rows
	ResultCount   int                      // Number of results found
	IndexHits     int                      // Number of index lookups performed
	ColumnsCount  int                      // Number of columns retrieved per row
}

// IndexStats represents statistics about an index
type IndexStats struct {
	Approach         IndexingApproach
	ColumnName       string
	RecordCount      uint32
	UniqueValues     uint32
	FileSize         int64
	CompressionRatio float64
	BuildTime        time.Duration
	MemoryUsage      int64
	Features         []string
}

// BenchmarkResult represents performance benchmark results
type BenchmarkResult struct {
	Approach         IndexingApproach
	DatasetSize      int
	IndexBuildTime   time.Duration
	IndexSize        int64
	AvgQueryTime     time.Duration
	MinQueryTime     time.Duration
	MaxQueryTime     time.Duration
	QueriesPerSecond float64
	MemoryUsage      int64
	CompressionRatio float64
	Features         []string
	Errors           []string
}

// IndexGenerator interface defines the contract for index generation
type IndexGenerator interface {
	// GetApproach returns the indexing approach identifier
	GetApproach() IndexingApproach

	// GetFeatures returns a list of features supported by this approach
	GetFeatures() []string

	// GenerateIndex creates an index for the given column data
	GenerateIndex(columnName string, records []Record, outputDir string) (*IndexStats, error)

	// GenerateMultiColumnIndex creates indexes for multiple columns
	GenerateMultiColumnIndex(columnData map[string][]Record, outputDir string, tableName string) (map[string]*IndexStats, error)

	// ValidateIndex validates the integrity of an index file
	ValidateIndex(indexPath string) error

	// GetIndexStats returns statistics about an existing index
	GetIndexStats(indexPath string) (*IndexStats, error)
}

// QueryEngine interface defines the contract for query execution
type QueryEngine interface {
	// GetApproach returns the indexing approach identifier
	GetApproach() IndexingApproach

	// Initialize prepares the query engine with index directory
	Initialize(indexDir string) error

	// ExecuteQuery executes a single filter query
	ExecuteQuery(columnName string, filter QueryFilter) (*QueryResult, error)

	// ExecuteMultiColumnQuery executes a query with multiple filters
	ExecuteMultiColumnQuery(filters []QueryFilter) (*QueryResult, error)

	// ExecuteBatchQueries executes multiple queries and returns aggregated results
	ExecuteBatchQueries(queries []QueryFilter) ([]*QueryResult, error)

	// GetSupportedOperators returns the list of supported query operators
	GetSupportedOperators() []string

	// ExecuteQueryWithRows executes a query and returns complete rows with selected columns
	ExecuteQueryWithRows(columnName string, filter QueryFilter, selectColumns []string) (*QueryRowResult, error)

	// Close releases any resources held by the query engine
	Close() error
}

// IndexingImplementation combines generator and query engine
type IndexingImplementation struct {
	Generator   IndexGenerator
	QueryEngine QueryEngine
	Name        string
	Description string
	Version     string
}

// PerformanceBenchmark interface for running comprehensive benchmarks
type PerformanceBenchmark interface {
	// RunBenchmark executes a comprehensive benchmark for an implementation
	RunBenchmark(impl *IndexingImplementation, testData TestDataset) (*BenchmarkResult, error)

	// RunComparison runs benchmarks for multiple implementations
	RunComparison(implementations []*IndexingImplementation, testData TestDataset) ([]*BenchmarkResult, error)

	// GenerateReport creates a detailed performance report
	GenerateReport(results []*BenchmarkResult) (string, error)
}

// TestDataset represents a dataset for benchmarking
type TestDataset struct {
	Name        string
	Size        int
	ColumnData  map[string][]Record
	QuerySet    []QueryFilter
	Description string
}

// ApproachRegistry manages available indexing approaches
type ApproachRegistry interface {
	// RegisterImplementation adds a new indexing implementation
	RegisterImplementation(impl *IndexingImplementation) error

	// GetImplementation retrieves an implementation by approach
	GetImplementation(approach IndexingApproach) (*IndexingImplementation, error)

	// GetAllImplementations returns all registered implementations
	GetAllImplementations() []*IndexingImplementation

	// GetAvailableApproaches returns list of available approach identifiers
	GetAvailableApproaches() []IndexingApproach
}

// DataGenerator interface for creating test datasets
type DataGenerator interface {
	// GenerateRealisticData creates realistic test data with various patterns
	GenerateRealisticData(numRows int, schema map[string]string) (TestDataset, error)

	// GenerateQuerySet creates a set of queries for testing
	GenerateQuerySet(dataset TestDataset, numQueries int) ([]QueryFilter, error)

	// LoadFromCSV loads data from a CSV file
	LoadFromCSV(filename string) (TestDataset, error)
}

// Configuration for different implementations
type IndexConfig struct {
	Approach         IndexingApproach
	CompressionLevel int
	BloomFilterFPR   float64
	BlockSize        int
	CacheSize        int64
	EnableMmap       bool
	CustomParams     map[string]interface{}
}

// MetricsCollector for gathering detailed performance metrics
type MetricsCollector interface {
	// StartCollection begins metrics collection
	StartCollection() error

	// StopCollection ends metrics collection and returns results
	StopCollection() (map[string]interface{}, error)

	// GetMemoryUsage returns current memory usage
	GetMemoryUsage() int64

	// GetDiskIO returns disk I/O statistics
	GetDiskIO() (reads int64, writes int64)

	// Reset clears all collected metrics
	Reset()
}
