package main

import (
	"encoding/binary"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"sort"
	"strings"
	"time"
	fileio "ultrafast/existing_inx/file_io"
)

// ExistingCompressedGenerator implements IndexGenerator for the existing compressed format
type ExistingCompressedGenerator struct {
	outputDir string
}

// NewExistingCompressedGenerator creates a new generator for existing compressed format
func NewExistingCompressedGenerator(outputDir string) *ExistingCompressedGenerator {
	return &ExistingCompressedGenerator{
		outputDir: outputDir,
	}
}

// GetApproach returns the indexing approach
func (g *ExistingCompressedGenerator) GetApproach() IndexingApproach {
	return ApproachExistingInx
}

// GetFeatures returns a list of features supported by this approach
func (g *ExistingCompressedGenerator) GetFeatures() []string {
	return []string{
		"Compressed Index Format",
		"Unique Value Lists",
		"Line Number Arrays",
		"Search Optimization",
		"Space Efficient Storage",
	}
}

// GenerateIndex creates an index for the given column data
func (g *ExistingCompressedGenerator) GenerateIndex(columnName string, records []Record, outputDir string) (*IndexStats, error) {
	start := time.Now()

	// Update output directory
	g.outputDir = outputDir

	// Ensure output directory exists
	if err := os.MkdirAll(g.outputDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create output directory: %v", err)
	}

	// Create search map and inverted index data
	searchMap := make(map[string][]int)
	invertedInxData := make([]fileio.Record, 0, len(records))

	// Sort records by line number
	sort.Slice(records, func(i, j int) bool {
		return records[i].LineNumber < records[j].LineNumber
	})

	// Build search map and inverted index
	for _, record := range records {
		lineNum := int(record.LineNumber)
		value := record.Value

		// Add to search map
		if _, exists := searchMap[value]; !exists {
			searchMap[value] = []int{lineNum}
		} else {
			searchMap[value] = append(searchMap[value], lineNum)
		}

		// Add to inverted index data
		invertedInxData = append(invertedInxData, fileio.Record{
			LineNumber: record.LineNumber,
			VLen:       uint32(len(value)),
			Value:      value,
		})
	}

	// Create search subdirectory
	searchDir := filepath.Join(g.outputDir, "search")
	if err := os.MkdirAll(searchDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create search directory: %v", err)
	}

	// Create the compressed index file directly
	fileName := fmt.Sprintf("%s_inx_com.txt", columnName)
	filePath := filepath.Join(searchDir, fileName)

	err := g.createCompressedIndexFile(filePath, searchMap, invertedInxData)
	if err != nil {
		return nil, fmt.Errorf("failed to create compressed index for column %s: %v", columnName, err)
	}

	// Get file size
	var fileSize int64
	if stat, err := os.Stat(filePath); err == nil {
		fileSize = stat.Size()
	}

	// Calculate compression ratio (compared to raw data size)
	rawSize := int64(0)
	for _, record := range records {
		rawSize += int64(len(record.Value)) + 8 // value + line number + length
	}
	compressionRatio := float64(rawSize) / float64(fileSize)

	buildTime := time.Since(start)

	return &IndexStats{
		Approach:         ApproachExistingInx,
		ColumnName:       columnName,
		RecordCount:      uint32(len(records)),
		UniqueValues:     uint32(len(searchMap)),
		FileSize:         fileSize,
		CompressionRatio: compressionRatio,
		BuildTime:        buildTime,
		Features:         g.GetFeatures(),
	}, nil
}

// GenerateMultiColumnIndex creates indexes for multiple columns
func (g *ExistingCompressedGenerator) GenerateMultiColumnIndex(columnData map[string][]Record, outputDir string, tableName string) (map[string]*IndexStats, error) {
	g.outputDir = outputDir
	results := make(map[string]*IndexStats)

	for columnName, records := range columnData {
		stats, err := g.GenerateIndex(columnName, records, outputDir)
		if err != nil {
			return nil, fmt.Errorf("failed to generate index for column %s: %v", columnName, err)
		}
		results[columnName] = stats
	}

	return results, nil
}

// ValidateIndex validates the integrity of an index file
func (g *ExistingCompressedGenerator) ValidateIndex(indexPath string) error {
	file, err := os.Open(indexPath)
	if err != nil {
		return fmt.Errorf("failed to open index file: %v", err)
	}
	defer file.Close()

	// Read and validate header
	startByteOffsetBuff := make([]byte, 8)
	_, err = file.Read(startByteOffsetBuff)
	if err != nil {
		return fmt.Errorf("failed to read header: %v", err)
	}

	endOffsetIndexBlock := binary.BigEndian.Uint32(startByteOffsetBuff[0:4])
	startOffsetIndexBlock := binary.BigEndian.Uint32(startByteOffsetBuff[4:8])

	// Basic validation - check if offsets are reasonable
	if startOffsetIndexBlock < 8 || endOffsetIndexBlock < startOffsetIndexBlock {
		return fmt.Errorf("invalid file structure: corrupted offsets")
	}

	return nil
}

// GetIndexStats returns statistics about an existing index
func (g *ExistingCompressedGenerator) GetIndexStats(indexPath string) (*IndexStats, error) {
	file, err := os.Open(indexPath)
	if err != nil {
		return nil, fmt.Errorf("failed to open index file: %v", err)
	}
	defer file.Close()

	// Get file size
	fileInfo, err := file.Stat()
	if err != nil {
		return nil, err
	}

	// Read header to get basic structure info
	startByteOffsetBuff := make([]byte, 8)
	_, err = file.Read(startByteOffsetBuff)
	if err != nil {
		return nil, err
	}

	endOffsetIndexBlock := binary.BigEndian.Uint32(startByteOffsetBuff[0:4])
	startOffsetIndexBlock := binary.BigEndian.Uint32(startByteOffsetBuff[4:8])

	// Estimate record count from index block
	file.Seek(int64(endOffsetIndexBlock), io.SeekStart)
	buffer := make([]byte, 4)
	_, err = file.Read(buffer)
	if err != nil {
		return nil, err
	}

	recordCount := binary.BigEndian.Uint32(buffer)

	// Estimate unique values from unique value list block size
	uniqValueListBlockSize := startOffsetIndexBlock - 8
	estimatedUniqueValues := uniqValueListBlockSize / 20 // rough estimate

	return &IndexStats{
		Approach:         ApproachExistingInx,
		RecordCount:      recordCount,
		UniqueValues:     uint32(estimatedUniqueValues),
		FileSize:         fileInfo.Size(),
		CompressionRatio: 0.5, // Estimated compression ratio
		Features:         g.GetFeatures(),
	}, nil
}

// createCompressedIndexFile creates a compressed index file directly without using the file_io package
func (g *ExistingCompressedGenerator) createCompressedIndexFile(filePath string, searchMap map[string][]int, invertedInxData []fileio.Record) error {
	file, err := os.Create(filePath)
	if err != nil {
		return fmt.Errorf("failed to create file: %v", err)
	}
	defer file.Close()

	// Calculate offsets
	headerSize := uint32(8) // 8 bytes for the header
	uniqueValueBlockSize := uint32(0)

	// Calculate unique value block size
	for value := range searchMap {
		uniqueValueBlockSize += 4 + 4 + uint32(len(value)) // value_len + line_list_offset + value
	}

	startOffsetIndexBlock := headerSize + uniqueValueBlockSize

	// Calculate line list block size
	lineListBlockSize := uint32(0)
	for _, lineNumbers := range searchMap {
		lineListBlockSize += 4 + uint32(len(lineNumbers)*4) // count + line_numbers
	}

	endOffsetIndexBlock := startOffsetIndexBlock + lineListBlockSize

	// Write header (8 bytes)
	header := make([]byte, 8)
	binary.BigEndian.PutUint32(header[0:4], endOffsetIndexBlock)
	binary.BigEndian.PutUint32(header[4:8], startOffsetIndexBlock)
	if _, err := file.Write(header); err != nil {
		return err
	}

	// Write unique value block
	currentLineListOffset := startOffsetIndexBlock
	for value, lineNumbers := range searchMap {
		// Write value length
		valueLen := uint32(len(value))
		if err := binary.Write(file, binary.BigEndian, valueLen); err != nil {
			return err
		}

		// Write line list offset
		if err := binary.Write(file, binary.BigEndian, currentLineListOffset); err != nil {
			return err
		}

		// Write value
		if _, err := file.Write([]byte(value)); err != nil {
			return err
		}

		// Update offset for next line list
		currentLineListOffset += 4 + uint32(len(lineNumbers)*4)
	}

	// Write line list block
	for _, lineNumbers := range searchMap {
		// Write count
		count := uint32(len(lineNumbers))
		if err := binary.Write(file, binary.BigEndian, count); err != nil {
			return err
		}

		// Write line numbers
		for _, lineNum := range lineNumbers {
			if err := binary.Write(file, binary.BigEndian, uint32(lineNum)); err != nil {
				return err
			}
		}
	}

	// Write inverted index block
	indexBlockCount := uint32(len(invertedInxData))
	if err := binary.Write(file, binary.BigEndian, indexBlockCount); err != nil {
		return err
	}

	for _, record := range invertedInxData {
		// Write line number
		if err := binary.Write(file, binary.BigEndian, record.LineNumber); err != nil {
			return err
		}

		// Write value offset (we'll use a simple offset calculation)
		valueOffset := uint32(0) // Simplified for now
		if err := binary.Write(file, binary.BigEndian, valueOffset); err != nil {
			return err
		}

		// Write value length
		if err := binary.Write(file, binary.BigEndian, record.VLen); err != nil {
			return err
		}
	}

	return nil
}

// ExistingCompressedQueryEngine implements QueryEngine for the existing compressed format
type ExistingCompressedQueryEngine struct {
	indexDir string
}

// NewExistingCompressedQueryEngine creates a new query engine for existing compressed format
func NewExistingCompressedQueryEngine(indexDir string) *ExistingCompressedQueryEngine {
	return &ExistingCompressedQueryEngine{
		indexDir: indexDir,
	}
}

// GetApproach returns the indexing approach
func (e *ExistingCompressedQueryEngine) GetApproach() IndexingApproach {
	return ApproachExistingInx
}

// GetSupportedOperators returns supported query operators
func (e *ExistingCompressedQueryEngine) GetSupportedOperators() []string {
	return []string{"=", "!=", "LIKE", "IN"}
}

// Initialize prepares the query engine with index directory
func (e *ExistingCompressedQueryEngine) Initialize(indexDir string) error {
	e.indexDir = indexDir
	return nil
}

// ExecuteQuery executes a single filter query
func (e *ExistingCompressedQueryEngine) ExecuteQuery(columnName string, filter QueryFilter) (*QueryResult, error) {
	if filter.Operator != "=" {
		return nil, fmt.Errorf("existing compressed format only supports equality queries, got: %s", filter.Operator)
	}

	start := time.Now()

	// Find matching line numbers
	matchingLines, err := e.findMatchingLines(columnName, filter.Value)
	if err != nil {
		return nil, fmt.Errorf("failed to find matching lines: %v", err)
	}

	return &QueryResult{
		LineNumbers:   matchingLines,
		ExecutionTime: time.Since(start),
		ResultCount:   len(matchingLines),
		IndexHits:     1,
	}, nil
}

// ExecuteMultiColumnQuery executes a query with multiple filters
func (e *ExistingCompressedQueryEngine) ExecuteMultiColumnQuery(filters []QueryFilter) (*QueryResult, error) {
	if len(filters) == 0 {
		return &QueryResult{}, nil
	}

	start := time.Now()
	var resultLineNumbers []uint32
	var totalIndexHits int

	// Execute first query
	firstResult, err := e.ExecuteQuery(filters[0].Column, filters[0])
	if err != nil {
		return nil, err
	}

	resultLineNumbers = firstResult.LineNumbers
	totalIndexHits += firstResult.IndexHits

	// Intersect with remaining queries
	for i := 1; i < len(filters); i++ {
		if len(resultLineNumbers) == 0 {
			break // No point continuing if no results
		}

		queryResult, err := e.ExecuteQuery(filters[i].Column, filters[i])
		if err != nil {
			return nil, err
		}

		totalIndexHits += queryResult.IndexHits

		// Intersect line numbers
		resultLineNumbers = e.intersectLineNumbers(resultLineNumbers, queryResult.LineNumbers)
	}

	return &QueryResult{
		LineNumbers:   resultLineNumbers,
		ExecutionTime: time.Since(start),
		ResultCount:   len(resultLineNumbers),
		IndexHits:     totalIndexHits,
	}, nil
}

// ExecuteBatchQueries executes multiple queries and returns aggregated results
func (e *ExistingCompressedQueryEngine) ExecuteBatchQueries(queries []QueryFilter) ([]*QueryResult, error) {
	results := make([]*QueryResult, len(queries))

	for i, query := range queries {
		result, err := e.ExecuteQuery(query.Column, query)
		if err != nil {
			return nil, fmt.Errorf("batch query %d failed: %v", i, err)
		}
		results[i] = result
	}

	return results, nil
}

// intersectLineNumbers finds the intersection of two sorted line number arrays
func (e *ExistingCompressedQueryEngine) intersectLineNumbers(a, b []uint32) []uint32 {
	result := make([]uint32, 0, min(len(a), len(b)))
	i, j := 0, 0

	for i < len(a) && j < len(b) {
		if a[i] == b[j] {
			result = append(result, a[i])
			i++
			j++
		} else if a[i] < b[j] {
			i++
		} else {
			j++
		}
	}

	return result
}

// findMatchingLines finds line numbers that match the filter using compressed index
func (e *ExistingCompressedQueryEngine) findMatchingLines(columnName string, filterValue interface{}) ([]uint32, error) {
	searchString := fmt.Sprintf("%v", filterValue)
	filename := fmt.Sprintf("%s_inx_com.txt", columnName)
	filepath := filepath.Join(e.indexDir, "search", filename)

	file, err := os.Open(filepath)
	if err != nil {
		return nil, fmt.Errorf("failed to open file %s: %v", filename, err)
	}
	defer file.Close()

	// Read first 8 bytes to get block offsets
	startByteOffsetBuff := make([]byte, 8)
	_, err = file.Read(startByteOffsetBuff)
	if err != nil {
		return nil, fmt.Errorf("failed to read header: %v", err)
	}

	endOffsetIndexBlock := binary.BigEndian.Uint32(startByteOffsetBuff[0:4])
	startOffsetIndexBlock := binary.BigEndian.Uint32(startByteOffsetBuff[4:8])

	// Calculate unique value list block size
	uniqValueListBlockSize := startOffsetIndexBlock - 8

	// Find the offset for the search string
	lineListOffset := e.getListOffset(searchString, file, int(uniqValueListBlockSize))
	if lineListOffset == 0 {
		return []uint32{}, nil // No matches found
	}

	// Read the line numbers from the found offset
	return e.readLineNumbers(file, lineListOffset, endOffsetIndexBlock)
}

// getListOffset finds the offset for a specific search string in the unique values block
func (e *ExistingCompressedQueryEngine) getListOffset(searchString string, file *os.File, uniqValueListBlockSize int) uint32 {
	// Seek to start of unique values block (after 8-byte header)
	file.Seek(8, io.SeekStart)

	buffer := make([]byte, uniqValueListBlockSize)
	_, err := file.Read(buffer)
	if err != nil {
		return 0
	}

	offset := 0
	for offset < len(buffer) {
		if offset+8 > len(buffer) {
			break
		}

		// Read value length and line list offset
		valueLen := binary.BigEndian.Uint32(buffer[offset : offset+4])
		lineListOffset := binary.BigEndian.Uint32(buffer[offset+4 : offset+8])
		offset += 8

		if offset+int(valueLen) > len(buffer) {
			break
		}

		// Read the value
		value := string(buffer[offset : offset+int(valueLen)])
		offset += int(valueLen)

		if strings.EqualFold(searchString, value) {
			return lineListOffset
		}
	}

	return 0
}

// readLineNumbers reads line numbers from the specified offset
func (e *ExistingCompressedQueryEngine) readLineNumbers(file *os.File, offset uint32, endOffset uint32) ([]uint32, error) {
	file.Seek(int64(offset), io.SeekStart)

	// Read number of line numbers
	countBuffer := make([]byte, 4)
	_, err := file.Read(countBuffer)
	if err != nil {
		return nil, err
	}

	count := binary.BigEndian.Uint32(countBuffer)
	lineNumbers := make([]uint32, count)

	// Read all line numbers
	for i := uint32(0); i < count; i++ {
		lineBuffer := make([]byte, 4)
		_, err := file.Read(lineBuffer)
		if err != nil {
			return nil, err
		}
		lineNumbers[i] = binary.BigEndian.Uint32(lineBuffer)
	}

	return lineNumbers, nil
}

// getCompleteRows retrieves complete rows for given line numbers
func (e *ExistingCompressedQueryEngine) getCompleteRows(lineNumbers []uint32, selectColumns []string) ([]map[string]interface{}, error) {
	if len(selectColumns) == 0 {
		// Default columns if none specified
		selectColumns = []string{"timestamp", "source_ip", "destination_ip", "protocol", "action"}
	}

	// Limit to first 10 rows for performance demonstration
	limitedLineNumbers := lineNumbers
	if len(lineNumbers) > 10 {
		limitedLineNumbers = lineNumbers[:10]
	}

	results := make([]map[string]interface{}, 0, len(limitedLineNumbers))

	for _, lineNumber := range limitedLineNumbers {
		row := make(map[string]interface{})
		row["line_number"] = lineNumber

		// Get values for each selected column
		for _, columnName := range selectColumns {
			value, err := e.getValueForLine(columnName, lineNumber)
			if err != nil {
				// If column doesn't exist or error, set error message
				row[columnName] = fmt.Sprintf("[Error: %v]", err)
			} else {
				row[columnName] = value
			}
		}

		results = append(results, row)
	}

	return results, nil
}

// ExecuteQueryWithRows executes a query and returns complete rows with selected columns
func (e *ExistingCompressedQueryEngine) ExecuteQueryWithRows(columnName string, filter QueryFilter, selectColumns []string) (*QueryRowResult, error) {
	start := time.Now()

	// First execute the regular query to get line numbers
	queryResult, err := e.ExecuteQuery(columnName, filter)
	if err != nil {
		return nil, err
	}

	// Get complete rows for the matching line numbers
	rows, err := e.getCompleteRows(queryResult.LineNumbers, selectColumns)
	if err != nil {
		return nil, fmt.Errorf("failed to get complete rows: %v", err)
	}

	return &QueryRowResult{
		Rows:          rows,
		LineNumbers:   queryResult.LineNumbers,
		ExecutionTime: time.Since(start),
		ResultCount:   len(rows),
		IndexHits:     queryResult.IndexHits + len(selectColumns)*len(queryResult.LineNumbers), // Additional hits for column lookups
		ColumnsCount:  len(selectColumns),
	}, nil
}

// getValueForLine gets the value for a specific column and line number from compressed index
func (e *ExistingCompressedQueryEngine) getValueForLine(columnName string, targetLine uint32) (string, error) {
	filename := fmt.Sprintf("%s_inx_com.txt", columnName)
	filepath := filepath.Join(e.indexDir, "search", filename)

	file, err := os.Open(filepath)
	if err != nil {
		return "", fmt.Errorf("failed to open file %s: %v", filename, err)
	}
	defer file.Close()

	// Read header to get inverted index block offset
	startByteOffsetBuff := make([]byte, 8)
	_, err = file.Read(startByteOffsetBuff)
	if err != nil {
		return "", err
	}

	endOffsetIndexBlock := binary.BigEndian.Uint32(startByteOffsetBuff[0:4])

	// Seek to inverted index block
	file.Seek(int64(endOffsetIndexBlock), io.SeekStart)

	// Read inverted index to find the value for target line
	buffer := make([]byte, 4)
	_, err = file.Read(buffer)
	if err != nil {
		return "", err
	}

	indexBlock := binary.BigEndian.Uint32(buffer)

	// Search through inverted index
	for i := uint32(0); i < indexBlock; i++ {
		// Read line number
		_, err := file.Read(buffer)
		if err != nil {
			return "", err
		}
		lineNumber := binary.BigEndian.Uint32(buffer)

		// Read value offset
		_, err = file.Read(buffer)
		if err != nil {
			return "", err
		}
		valueOffset := binary.BigEndian.Uint32(buffer)

		// Read value length
		_, err = file.Read(buffer)
		if err != nil {
			return "", err
		}
		valueLen := binary.BigEndian.Uint32(buffer)

		if lineNumber == targetLine {
			// Found the target line, read the value
			valueBuffer := make([]byte, valueLen)
			file.Seek(int64(valueOffset), io.SeekStart)
			_, err := file.Read(valueBuffer)
			if err != nil {
				return "", err
			}
			return string(valueBuffer), nil
		}
	}

	return "", fmt.Errorf("line number %d not found in column %s", targetLine, columnName)
}

// Close releases any resources
func (e *ExistingCompressedQueryEngine) Close() error {
	return nil
}
