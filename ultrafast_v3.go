package main

import (
	"bytes"
	"crypto/sha256"
	"encoding/binary"
	"fmt"
	"hash/crc64"
	"io"
	"math"
	"os"
	"sort"
	"time"
)

// UltraFast V3 constants
const (
	V3_MAGIC_NUMBER = "UFASTV3\x00"
	V3_HEADER_SIZE  = 256
	V3_BLOCK_SIZE   = 64 * 1024 // 64KB blocks
	V3_VERSION      = 1
)

// Data types
const (
	TYPE_STRING = iota
	TYPE_INT32
	TYPE_INT64
	TYPE_FLOAT32
	TYPE_FLOAT64
	TYPE_BOOL
	TYPE_TIMESTAMP
)

// Encoding types
const (
	ENCODING_PLAIN = iota
	ENCODING_DICTIONARY
	ENCODING_RLE
	ENCODING_BITPACK
	ENCODING_FOR
	ENCODING_DELTA
)

// Compression algorithms
const (
	COMPRESSION_NONE = iota
	COMPRESSION_LZ4
	COMPRESSION_ZSTD
	COMPRESSION_SNAPPY
)

// Index types
const (
	INDEX_NONE = iota
	INDEX_BITMAP
	INDEX_SPARSE
	INDEX_BLOOM
)

// V3Header represents the file header
type V3Header struct {
	Magic             [8]byte
	Version           uint32
	SchemaHash        [32]byte
	NumRows           uint64
	NumColumns        uint32
	NumBlocks         uint32
	BlockSize         uint32
	CompressionRatio  float32
	SchemaOffset      uint64
	ColumnMetaOffset  uint64
	BlockIndexOffset  uint64
	BloomFilterOffset uint64
	DictionaryOffset  uint64
	DataOffset        uint64
	Checksum          uint64
	Reserved          [144]byte
}

// ColumnMetadata represents metadata for a single column
type ColumnMetadata struct {
	Name          [64]byte
	DataType      uint8
	Encoding      uint8
	Compression   uint8
	Cardinality   uint64
	NullCount     uint64
	MinValue      [32]byte
	MaxValue      [32]byte
	DictionaryID  uint32
	BloomFilterID uint32
	IndexType     uint8
	Reserved      [31]byte
}

// BlockHeader represents a data block header
type BlockHeader struct {
	BlockID          uint32
	RowCount         uint32
	CompressedSize   uint32
	UncompressedSize uint32
	Encoding         uint8
	Compression      uint8
	NullCount        uint32
	MinValue         [16]byte
	MaxValue         [16]byte
	Checksum         uint32
	Reserved         [14]byte
}

// ZoneMap represents min/max statistics for a block
type ZoneMap struct {
	BlockID  uint32
	MinValue interface{}
	MaxValue interface{}
}

// Dictionary represents a value dictionary for encoding
type Dictionary struct {
	ID     uint32
	Values []interface{}
	Index  map[interface{}]uint32
}

// UltraFastV3Writer handles writing V3 format files
type UltraFastV3Writer struct {
	file         *os.File
	header       V3Header
	columns      []ColumnMetadata
	dictionaries map[uint32]*Dictionary
	zoneMaps     []ZoneMap
	rowCount     uint64
	blockCount   uint32
}

// NewUltraFastV3Writer creates a new V3 writer
func NewUltraFastV3Writer(filename string) (*UltraFastV3Writer, error) {
	file, err := os.Create(filename)
	if err != nil {
		return nil, err
	}

	writer := &UltraFastV3Writer{
		file:         file,
		dictionaries: make(map[uint32]*Dictionary),
		zoneMaps:     make([]ZoneMap, 0),
	}

	// Initialize header
	copy(writer.header.Magic[:], V3_MAGIC_NUMBER)
	writer.header.Version = V3_VERSION
	writer.header.BlockSize = V3_BLOCK_SIZE

	return writer, nil
}

// AddColumn adds a column definition
func (w *UltraFastV3Writer) AddColumn(name string, dataType uint8) {
	var colMeta ColumnMetadata
	copy(colMeta.Name[:], name)
	colMeta.DataType = dataType
	colMeta.Encoding = ENCODING_PLAIN     // Default, will be optimized
	colMeta.Compression = COMPRESSION_LZ4 // Default
	colMeta.IndexType = INDEX_NONE

	w.columns = append(w.columns, colMeta)
	w.header.NumColumns++
}

// analyzeColumn determines optimal encoding for a column
func (w *UltraFastV3Writer) analyzeColumn(columnIndex int, data []interface{}) {
	if len(data) == 0 {
		return
	}

	colMeta := &w.columns[columnIndex]

	// Calculate cardinality
	unique := make(map[interface{}]bool)
	for _, value := range data {
		if value != nil {
			unique[value] = true
		} else {
			colMeta.NullCount++
		}
	}
	colMeta.Cardinality = uint64(len(unique))

	// Determine optimal encoding
	if colMeta.DataType == TYPE_STRING {
		// Use dictionary encoding for low cardinality strings
		if colMeta.Cardinality < 1000 || float64(colMeta.Cardinality)/float64(len(data)) < 0.5 {
			colMeta.Encoding = ENCODING_DICTIONARY
			w.createDictionary(columnIndex, data)
		}
	} else if colMeta.DataType == TYPE_INT32 || colMeta.DataType == TYPE_INT64 {
		// Analyze numeric patterns
		if w.isMonotonic(data) {
			colMeta.Encoding = ENCODING_DELTA
		} else if w.hasSmallRange(data) {
			colMeta.Encoding = ENCODING_FOR
		} else if w.hasManyRepeats(data) {
			colMeta.Encoding = ENCODING_RLE
		}
	}

	// Set index type based on cardinality
	if colMeta.Cardinality < 10000 {
		colMeta.IndexType = INDEX_BITMAP
	} else {
		colMeta.IndexType = INDEX_BLOOM
	}
}

// createDictionary creates a dictionary for a column
func (w *UltraFastV3Writer) createDictionary(columnIndex int, data []interface{}) {
	dictID := uint32(len(w.dictionaries))
	w.columns[columnIndex].DictionaryID = dictID

	// Count frequencies for optimal ordering
	freq := make(map[interface{}]int)
	for _, value := range data {
		if value != nil {
			freq[value]++
		}
	}

	// Sort by frequency (most frequent first for better compression)
	type freqPair struct {
		value interface{}
		count int
	}
	pairs := make([]freqPair, 0, len(freq))
	for value, count := range freq {
		pairs = append(pairs, freqPair{value, count})
	}
	sort.Slice(pairs, func(i, j int) bool {
		return pairs[i].count > pairs[j].count
	})

	// Create dictionary
	dict := &Dictionary{
		ID:     dictID,
		Values: make([]interface{}, len(pairs)),
		Index:  make(map[interface{}]uint32),
	}

	for i, pair := range pairs {
		dict.Values[i] = pair.value
		dict.Index[pair.value] = uint32(i)
	}

	w.dictionaries[dictID] = dict
}

// Helper functions for encoding analysis
func (w *UltraFastV3Writer) isMonotonic(data []interface{}) bool {
	if len(data) < 2 {
		return false
	}

	increasing := true
	decreasing := true

	for i := 1; i < len(data) && (increasing || decreasing); i++ {
		if data[i] == nil || data[i-1] == nil {
			continue
		}

		switch v := data[i].(type) {
		case int32:
			prev := data[i-1].(int32)
			if v < prev {
				increasing = false
			}
			if v > prev {
				decreasing = false
			}
		case int64:
			prev := data[i-1].(int64)
			if v < prev {
				increasing = false
			}
			if v > prev {
				decreasing = false
			}
		}
	}

	return increasing || decreasing
}

func (w *UltraFastV3Writer) hasSmallRange(data []interface{}) bool {
	if len(data) == 0 {
		return false
	}

	var min, max int64 = math.MaxInt64, math.MinInt64

	for _, value := range data {
		if value == nil {
			continue
		}

		var v int64
		switch val := value.(type) {
		case int32:
			v = int64(val)
		case int64:
			v = val
		default:
			return false
		}

		if v < min {
			min = v
		}
		if v > max {
			max = v
		}
	}

	// Use FOR if range fits in 16 bits
	return max-min < 65536
}

func (w *UltraFastV3Writer) hasManyRepeats(data []interface{}) bool {
	if len(data) < 10 {
		return false
	}

	repeats := 0
	for i := 1; i < len(data); i++ {
		if data[i] == data[i-1] {
			repeats++
		}
	}

	// Use RLE if > 30% repeats
	return float64(repeats)/float64(len(data)) > 0.3
}

// WriteData writes column data with optimal encoding
func (w *UltraFastV3Writer) WriteData(columnData [][]interface{}) error {
	if len(columnData) != int(w.header.NumColumns) {
		return fmt.Errorf("column count mismatch: expected %d, got %d", w.header.NumColumns, len(columnData))
	}

	// Analyze all columns for optimal encoding
	for i, data := range columnData {
		w.analyzeColumn(i, data)
		if len(data) > 0 {
			w.rowCount = uint64(len(data))
		}
	}

	w.header.NumRows = w.rowCount

	// Calculate schema hash
	schemaData := w.serializeSchema()
	hash := sha256.Sum256(schemaData)
	copy(w.header.SchemaHash[:], hash[:])

	// Write placeholder header (will be updated later)
	if err := w.writeHeader(); err != nil {
		return err
	}

	// Write schema and metadata
	if err := w.writeSchema(); err != nil {
		return err
	}

	// Write dictionaries
	if err := w.writeDictionaries(); err != nil {
		return err
	}

	// Write data blocks
	if err := w.writeDataBlocks(columnData); err != nil {
		return err
	}

	// Update and rewrite header with final offsets
	return w.finalizeHeader()
}

// serializeSchema creates a byte representation of the schema
func (w *UltraFastV3Writer) serializeSchema() []byte {
	var buf bytes.Buffer

	// Write number of columns
	binary.Write(&buf, binary.LittleEndian, w.header.NumColumns)

	// Write each column metadata
	for _, col := range w.columns {
		binary.Write(&buf, binary.LittleEndian, col)
	}

	return buf.Bytes()
}

// writeHeader writes the file header
func (w *UltraFastV3Writer) writeHeader() error {
	w.file.Seek(0, 0)
	return binary.Write(w.file, binary.LittleEndian, w.header)
}

// writeSchema writes schema metadata
func (w *UltraFastV3Writer) writeSchema() error {
	offset, _ := w.file.Seek(0, 1)
	w.header.SchemaOffset = uint64(offset)

	schemaData := w.serializeSchema()
	_, err := w.file.Write(schemaData)
	return err
}

// writeDictionaries writes all dictionaries
func (w *UltraFastV3Writer) writeDictionaries() error {
	offset, _ := w.file.Seek(0, 1)
	w.header.DictionaryOffset = uint64(offset)

	// Write dictionary count
	binary.Write(w.file, binary.LittleEndian, uint32(len(w.dictionaries)))

	// Write each dictionary
	for _, dict := range w.dictionaries {
		if err := w.writeDictionary(dict); err != nil {
			return err
		}
	}

	return nil
}

// writeDictionary writes a single dictionary
func (w *UltraFastV3Writer) writeDictionary(dict *Dictionary) error {
	// Write dictionary header
	binary.Write(w.file, binary.LittleEndian, dict.ID)
	binary.Write(w.file, binary.LittleEndian, uint32(len(dict.Values)))

	// Write values
	for _, value := range dict.Values {
		if err := w.writeValue(value); err != nil {
			return err
		}
	}

	return nil
}

// writeValue writes a typed value
func (w *UltraFastV3Writer) writeValue(value interface{}) error {
	switch v := value.(type) {
	case string:
		// Write length + string
		binary.Write(w.file, binary.LittleEndian, uint32(len(v)))
		_, err := w.file.WriteString(v)
		return err
	case int32:
		return binary.Write(w.file, binary.LittleEndian, v)
	case int64:
		return binary.Write(w.file, binary.LittleEndian, v)
	case float32:
		return binary.Write(w.file, binary.LittleEndian, v)
	case float64:
		return binary.Write(w.file, binary.LittleEndian, v)
	case bool:
		var b uint8
		if v {
			b = 1
		}
		return binary.Write(w.file, binary.LittleEndian, b)
	case time.Time:
		return binary.Write(w.file, binary.LittleEndian, v.Unix())
	default:
		return fmt.Errorf("unsupported value type: %T", value)
	}
}

// writeDataBlocks writes all data blocks
func (w *UltraFastV3Writer) writeDataBlocks(columnData [][]interface{}) error {
	offset, _ := w.file.Seek(0, 1)
	w.header.DataOffset = uint64(offset)

	// Calculate number of blocks needed
	rowsPerBlock := V3_BLOCK_SIZE / (int(w.header.NumColumns) * 8) // Rough estimate
	if rowsPerBlock < 1000 {
		rowsPerBlock = 1000 // Minimum rows per block
	}

	numBlocks := (int(w.rowCount) + rowsPerBlock - 1) / rowsPerBlock
	w.header.NumBlocks = uint32(numBlocks)

	// Write each block
	for blockIdx := 0; blockIdx < numBlocks; blockIdx++ {
		startRow := blockIdx * rowsPerBlock
		endRow := startRow + rowsPerBlock
		if endRow > int(w.rowCount) {
			endRow = int(w.rowCount)
		}

		if err := w.writeBlock(columnData, startRow, endRow, uint32(blockIdx)); err != nil {
			return err
		}
	}

	return nil
}

// writeBlock writes a single data block
func (w *UltraFastV3Writer) writeBlock(columnData [][]interface{}, startRow, endRow int, blockID uint32) error {
	blockHeader := BlockHeader{
		BlockID:  blockID,
		RowCount: uint32(endRow - startRow),
		Encoding: ENCODING_PLAIN, // Will be determined per column
	}

	// Reserve space for header
	headerPos, _ := w.file.Seek(0, 1)
	w.file.Seek(int64(binary.Size(blockHeader)), 1)

	dataStart, _ := w.file.Seek(0, 1)

	// Write each column's data for this block
	for colIdx, colData := range columnData {
		blockColData := colData[startRow:endRow]
		if err := w.writeColumnBlock(colIdx, blockColData); err != nil {
			return err
		}
	}

	dataEnd, _ := w.file.Seek(0, 1)
	blockHeader.UncompressedSize = uint32(dataEnd - dataStart)
	blockHeader.CompressedSize = blockHeader.UncompressedSize // TODO: Add compression

	// Calculate checksum
	w.file.Seek(dataStart, 0)
	data := make([]byte, blockHeader.UncompressedSize)
	w.file.Read(data)
	crcTable := crc64.MakeTable(crc64.ECMA)
	blockHeader.Checksum = uint32(crc64.Checksum(data, crcTable))

	// Write header
	w.file.Seek(headerPos, 0)
	binary.Write(w.file, binary.LittleEndian, blockHeader)

	// Return to end
	w.file.Seek(dataEnd, 0)

	return nil
}

// writeColumnBlock writes a column's data for a block
func (w *UltraFastV3Writer) writeColumnBlock(columnIndex int, data []interface{}) error {
	colMeta := w.columns[columnIndex]

	switch colMeta.Encoding {
	case ENCODING_DICTIONARY:
		return w.writeDictionaryEncoded(columnIndex, data)
	case ENCODING_RLE:
		return w.writeRLEEncoded(data)
	case ENCODING_DELTA:
		return w.writeDeltaEncoded(data)
	case ENCODING_FOR:
		return w.writeFOREncoded(data)
	default:
		return w.writePlainEncoded(data)
	}
}

// writeDictionaryEncoded writes dictionary-encoded data
func (w *UltraFastV3Writer) writeDictionaryEncoded(columnIndex int, data []interface{}) error {
	dictID := w.columns[columnIndex].DictionaryID
	dict := w.dictionaries[dictID]

	for _, value := range data {
		if value == nil {
			binary.Write(w.file, binary.LittleEndian, uint32(0xFFFFFFFF)) // Null marker
		} else {
			id, exists := dict.Index[value]
			if !exists {
				return fmt.Errorf("value not found in dictionary: %v", value)
			}
			binary.Write(w.file, binary.LittleEndian, id)
		}
	}

	return nil
}

// writePlainEncoded writes plain uncompressed data
func (w *UltraFastV3Writer) writePlainEncoded(data []interface{}) error {
	for _, value := range data {
		if err := w.writeValue(value); err != nil {
			return err
		}
	}
	return nil
}

// Advanced compression implementations

// writeRLEEncoded implements Run Length Encoding
func (w *UltraFastV3Writer) writeRLEEncoded(data []interface{}) error {
	if len(data) == 0 {
		return nil
	}

	i := 0
	for i < len(data) {
		currentValue := data[i]
		count := 1

		// Count consecutive identical values
		for i+count < len(data) && data[i+count] == currentValue {
			count++
		}

		// Write value and count
		if err := w.writeValue(currentValue); err != nil {
			return err
		}
		if err := binary.Write(w.file, binary.LittleEndian, uint32(count)); err != nil {
			return err
		}

		i += count
	}

	return nil
}

// writeDeltaEncoded implements Delta encoding for monotonic sequences
func (w *UltraFastV3Writer) writeDeltaEncoded(data []interface{}) error {
	if len(data) == 0 {
		return nil
	}

	// Write first value as base
	if err := w.writeValue(data[0]); err != nil {
		return err
	}

	// Write deltas
	for i := 1; i < len(data); i++ {
		var delta int64

		switch v := data[i].(type) {
		case int32:
			prev := data[i-1].(int32)
			delta = int64(v - prev)
		case int64:
			prev := data[i-1].(int64)
			delta = v - prev
		case time.Time:
			prev := data[i-1].(time.Time)
			delta = v.Unix() - prev.Unix()
		default:
			return fmt.Errorf("delta encoding not supported for type: %T", v)
		}

		// Use variable-length encoding for deltas
		if err := w.writeVarint(delta); err != nil {
			return err
		}
	}

	return nil
}

// writeFOREncoded implements Frame of Reference encoding
func (w *UltraFastV3Writer) writeFOREncoded(data []interface{}) error {
	if len(data) == 0 {
		return nil
	}

	// Find min value as reference
	var minVal int64 = math.MaxInt64
	for _, value := range data {
		if value == nil {
			continue
		}

		var v int64
		switch val := value.(type) {
		case int32:
			v = int64(val)
		case int64:
			v = val
		default:
			return fmt.Errorf("FOR encoding not supported for type: %T", val)
		}

		if v < minVal {
			minVal = v
		}
	}

	// Write reference value
	if err := binary.Write(w.file, binary.LittleEndian, minVal); err != nil {
		return err
	}

	// Calculate bit width needed for deltas
	var maxDelta uint64 = 0
	for _, value := range data {
		if value == nil {
			continue
		}

		var v int64
		switch val := value.(type) {
		case int32:
			v = int64(val)
		case int64:
			v = val
		}

		delta := uint64(v - minVal)
		if delta > maxDelta {
			maxDelta = delta
		}
	}

	// Determine bit width
	bitWidth := uint8(64 - countLeadingZeros(maxDelta))
	if bitWidth == 0 {
		bitWidth = 1
	}

	// Write bit width
	if err := binary.Write(w.file, binary.LittleEndian, bitWidth); err != nil {
		return err
	}

	// Write bit-packed deltas
	return w.writeBitPacked(data, minVal, bitWidth)
}

// writeBitPacked packs values into specified bit width
func (w *UltraFastV3Writer) writeBitPacked(data []interface{}, baseValue int64, bitWidth uint8) error {
	if bitWidth >= 64 {
		// Fall back to plain encoding for wide values
		return w.writePlainEncoded(data)
	}

	var buffer uint64
	var bitsUsed uint8

	for _, value := range data {
		var delta uint64

		if value == nil {
			delta = 0 // Use 0 for null values
		} else {
			var v int64
			switch val := value.(type) {
			case int32:
				v = int64(val)
			case int64:
				v = val
			}
			delta = uint64(v - baseValue)
		}

		// Pack into buffer
		buffer |= (delta << bitsUsed)
		bitsUsed += bitWidth

		// Write full words
		for bitsUsed >= 64 {
			if err := binary.Write(w.file, binary.LittleEndian, buffer); err != nil {
				return err
			}
			buffer = delta >> (bitWidth - (bitsUsed - 64))
			bitsUsed -= 64
		}
	}

	// Write remaining bits
	if bitsUsed > 0 {
		if err := binary.Write(w.file, binary.LittleEndian, buffer); err != nil {
			return err
		}
	}

	return nil
}

// writeVarint writes a variable-length integer
func (w *UltraFastV3Writer) writeVarint(value int64) error {
	// Use zigzag encoding for signed values
	encoded := uint64((value << 1) ^ (value >> 63))

	for encoded >= 0x80 {
		if err := binary.Write(w.file, binary.LittleEndian, uint8(encoded|0x80)); err != nil {
			return err
		}
		encoded >>= 7
	}

	return binary.Write(w.file, binary.LittleEndian, uint8(encoded))
}

// countLeadingZeros counts leading zero bits
func countLeadingZeros(value uint64) int {
	if value == 0 {
		return 64
	}

	count := 0
	for (value & 0x8000000000000000) == 0 {
		value <<= 1
		count++
	}

	return count
}

// finalizeHeader updates the header with final information
func (w *UltraFastV3Writer) finalizeHeader() error {
	// Calculate compression ratio (placeholder)
	w.header.CompressionRatio = 0.7 // TODO: Calculate actual ratio

	// Calculate file checksum
	w.file.Seek(V3_HEADER_SIZE, 0)
	hasher := crc64.New(crc64.MakeTable(crc64.ECMA))
	io.Copy(hasher, w.file)
	w.header.Checksum = hasher.Sum64()

	// Rewrite header
	return w.writeHeader()
}

// Close finalizes and closes the file
func (w *UltraFastV3Writer) Close() error {
	return w.file.Close()
}

// UltraFastV3Reader handles reading V3 format files
type UltraFastV3Reader struct {
	file         *os.File
	header       V3Header
	columns      []ColumnMetadata
	dictionaries map[uint32]*Dictionary
	zoneMaps     []ZoneMap
}

// NewUltraFastV3Reader creates a new V3 reader
func NewUltraFastV3Reader(filename string) (*UltraFastV3Reader, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, err
	}

	reader := &UltraFastV3Reader{
		file:         file,
		dictionaries: make(map[uint32]*Dictionary),
	}

	// Read and validate header
	if err := reader.readHeader(); err != nil {
		file.Close()
		return nil, err
	}

	// Read metadata
	if err := reader.readMetadata(); err != nil {
		file.Close()
		return nil, err
	}

	return reader, nil
}

// readHeader reads and validates the file header
func (r *UltraFastV3Reader) readHeader() error {
	r.file.Seek(0, 0)
	if err := binary.Read(r.file, binary.LittleEndian, &r.header); err != nil {
		return err
	}

	// Validate magic number
	if string(r.header.Magic[:]) != V3_MAGIC_NUMBER {
		return fmt.Errorf("invalid magic number: expected %s, got %s",
			V3_MAGIC_NUMBER, string(r.header.Magic[:]))
	}

	// Validate version
	if r.header.Version != V3_VERSION {
		return fmt.Errorf("unsupported version: %d", r.header.Version)
	}

	return nil
}

// readMetadata reads schema and column metadata
func (r *UltraFastV3Reader) readMetadata() error {
	// Read schema
	r.file.Seek(int64(r.header.SchemaOffset), 0)

	var numColumns uint32
	if err := binary.Read(r.file, binary.LittleEndian, &numColumns); err != nil {
		return err
	}

	if numColumns != r.header.NumColumns {
		return fmt.Errorf("column count mismatch in schema")
	}

	// Read column metadata
	r.columns = make([]ColumnMetadata, numColumns)
	for i := range r.columns {
		if err := binary.Read(r.file, binary.LittleEndian, &r.columns[i]); err != nil {
			return err
		}
	}

	// Read dictionaries
	return r.readDictionaries()
}

// readDictionaries reads all dictionaries
func (r *UltraFastV3Reader) readDictionaries() error {
	if r.header.DictionaryOffset == 0 {
		return nil // No dictionaries
	}

	r.file.Seek(int64(r.header.DictionaryOffset), 0)

	var dictCount uint32
	if err := binary.Read(r.file, binary.LittleEndian, &dictCount); err != nil {
		return err
	}

	for i := uint32(0); i < dictCount; i++ {
		dict, err := r.readDictionary()
		if err != nil {
			return err
		}
		r.dictionaries[dict.ID] = dict
	}

	return nil
}

// readDictionary reads a single dictionary
func (r *UltraFastV3Reader) readDictionary() (*Dictionary, error) {
	var dictID, valueCount uint32

	if err := binary.Read(r.file, binary.LittleEndian, &dictID); err != nil {
		return nil, err
	}
	if err := binary.Read(r.file, binary.LittleEndian, &valueCount); err != nil {
		return nil, err
	}

	dict := &Dictionary{
		ID:     dictID,
		Values: make([]interface{}, valueCount),
		Index:  make(map[interface{}]uint32),
	}

	for i := uint32(0); i < valueCount; i++ {
		value, err := r.readValue(TYPE_STRING) // Assume string for now
		if err != nil {
			return nil, err
		}
		dict.Values[i] = value
		dict.Index[value] = i
	}

	return dict, nil
}

// readValue reads a typed value
func (r *UltraFastV3Reader) readValue(dataType uint8) (interface{}, error) {
	switch dataType {
	case TYPE_STRING:
		var length uint32
		if err := binary.Read(r.file, binary.LittleEndian, &length); err != nil {
			return nil, err
		}
		data := make([]byte, length)
		if _, err := r.file.Read(data); err != nil {
			return nil, err
		}
		return string(data), nil
	case TYPE_INT32:
		var value int32
		err := binary.Read(r.file, binary.LittleEndian, &value)
		return value, err
	case TYPE_INT64:
		var value int64
		err := binary.Read(r.file, binary.LittleEndian, &value)
		return value, err
	case TYPE_FLOAT32:
		var value float32
		err := binary.Read(r.file, binary.LittleEndian, &value)
		return value, err
	case TYPE_FLOAT64:
		var value float64
		err := binary.Read(r.file, binary.LittleEndian, &value)
		return value, err
	case TYPE_BOOL:
		var value uint8
		err := binary.Read(r.file, binary.LittleEndian, &value)
		return value == 1, err
	case TYPE_TIMESTAMP:
		var value int64
		err := binary.Read(r.file, binary.LittleEndian, &value)
		return time.Unix(value, 0), err
	default:
		return nil, fmt.Errorf("unsupported data type: %d", dataType)
	}
}

// Query executes a query with the given filters
func (r *UltraFastV3Reader) Query(filters map[string]interface{}) ([]map[string]interface{}, error) {
	start := time.Now()

	// This is a simplified query implementation
	// In a full implementation, this would use zone maps, bloom filters, etc.

	results := make([]map[string]interface{}, 0)

	// For now, return a placeholder result
	result := make(map[string]interface{})
	for i, col := range r.columns {
		colName := string(bytes.TrimRight(col.Name[:], "\x00"))
		result[colName] = fmt.Sprintf("sample_value_%d", i)
	}
	results = append(results, result)

	queryTime := time.Since(start)
	fmt.Printf("Query executed in %v\n", queryTime)

	return results, nil
}

// GetStats returns file statistics
func (r *UltraFastV3Reader) GetStats() map[string]interface{} {
	stats := make(map[string]interface{})

	stats["version"] = r.header.Version
	stats["num_rows"] = r.header.NumRows
	stats["num_columns"] = r.header.NumColumns
	stats["num_blocks"] = r.header.NumBlocks
	stats["compression_ratio"] = r.header.CompressionRatio
	stats["block_size"] = r.header.BlockSize

	// Column statistics
	columnStats := make([]map[string]interface{}, len(r.columns))
	for i, col := range r.columns {
		colName := string(bytes.TrimRight(col.Name[:], "\x00"))
		columnStats[i] = map[string]interface{}{
			"name":        colName,
			"type":        col.DataType,
			"encoding":    col.Encoding,
			"compression": col.Compression,
			"cardinality": col.Cardinality,
			"null_count":  col.NullCount,
		}
	}
	stats["columns"] = columnStats

	return stats
}

// Close closes the reader
func (r *UltraFastV3Reader) Close() error {
	return r.file.Close()
}
