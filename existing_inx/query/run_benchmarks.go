package main

import (
	"fmt"
	"log"
	"os"
)

// RunBenchmarkSuite runs comprehensive benchmarks (renamed to avoid main conflict)
func RunBenchmarkSuite() {
	if len(os.Args) > 1 && os.Args[1] == "benchmark" {
		runComprehensiveBenchmarks()
	} else {
		fmt.Println("Usage: go run . benchmark")
		fmt.Println("This will run comprehensive benchmarks comparing:")
		fmt.Println("  - Existing approach (binary format)")
		fmt.Println("  - ExistingInx approach (compressed index)")
		fmt.Println("  - UltraFast approach (memory-mapped)")
		fmt.Println("")
		fmt.Println("Results will include:")
		fmt.Println("  - Search-only performance")
		fmt.Println("  - Complete result retrieval performance")
		fmt.Println("  - Memory usage analysis")
		fmt.Println("  - Correctness validation")
	}
}

func runComprehensiveBenchmarks() {
	fmt.Println("🚀 Starting Comprehensive Indexing Benchmark Suite")
	fmt.Println("==================================================")

	// Create benchmark runner
	runner, err := NewBenchmarkRunner()
	if err != nil {
		log.Fatalf("Failed to create benchmark runner: %v", err)
	}

	fmt.Printf("📁 Results will be saved to: %s\n", runner.ResultsDir)
	fmt.Printf("🔍 Testing %d queries from search_string.txt\n", len(runner.Queries))

	// Display queries being tested
	fmt.Println("\n📋 Queries to test:")
	for i, query := range runner.Queries {
		fmt.Printf("  %d. %s = %s\n", i+1, query.Column, query.Value)
	}

	// Run comprehensive benchmarks
	err = runner.RunComprehensiveBenchmarks()
	if err != nil {
		log.Fatalf("Benchmark suite failed: %v", err)
	}

	fmt.Println("\n🎉 Benchmark suite completed successfully!")
	fmt.Printf("📊 View results in: %s\n", runner.ResultsDir)

	// Provide quick analysis commands
	fmt.Println("\n🔍 Quick Analysis Commands:")
	fmt.Printf("  # View search performance:\n")
	fmt.Printf("  cat %s/search_performance.csv\n", runner.ResultsDir)
	fmt.Printf("\n  # View retrieval performance:\n")
	fmt.Printf("  cat %s/retrieval_performance.csv\n", runner.ResultsDir)
	fmt.Printf("\n  # View comprehensive report:\n")
	fmt.Printf("  cat %s/comprehensive_report.md\n", runner.ResultsDir)
}
