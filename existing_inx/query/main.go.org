package main

import (
	"bytes"
	"encoding/binary"
	"encoding/csv"
	"fmt"
	fileio "index_poc/file_io"
	"io"
	"log"
	"strconv"
	"strings"
	"time"

	"os"

	"github.com/syndtr/goleveldb/leveldb"
	"github.com/xitongsys/parquet-go-source/local"
	"github.com/xitongsys/parquet-go/reader"
)

var getColList = []string{"timestamp", "source_ip", "destination_ip", "destination_location"}
var j = 0

type offsetAndLen struct {
	offset uint32
	len    uint32
}

// type finalResult struct {
// 	timestamp            string
// 	source_ip            string
// 	destination_ip       string
// 	destination_location string
// }

func main() {
	// Open the CSV file
	file, err := os.Open("/Users/<USER>/workspace/indexing_poc/search_string.txt")
	if err != nil {
		fmt.Println("Error opening file:", err)
		return
	}
	defer file.Close()

	// Create a new CSV reader with a custom comma (field separator)
	reader := csv.NewReader(file)
	reader.Comma = '=' // Set the field separator to semicolon (;)

	// Read all records from the CSV file
	records, err := reader.ReadAll()
	if err != nil {
		fmt.Println("Error reading CSV:", err)
		return
	}

	// Print each record
	for _, record := range records {
		col := record[0]
		searchString := record[1]
		t1 := time.Now()

		// getResultNew(col, searchString)
		// t2 := time.Now()
		// fmt.Println("GetResultNew ", col, t2.Sub(t1))

		// t1 = time.Now()

		getResultExisting(col, searchString)
		//fmt.Println(finalResultSetOld)
		t2 := time.Now()
		fmt.Println("GetResultExisting ", col, t2.Sub(t1))

		t1 = time.Now()
		getResultInx(col, searchString)
		t2 = time.Now()
		fmt.Println("GetResultInx ", col, t2.Sub(t1))

		// t1 = time.Now()

		// getResultParquet(col, searchString)
		// //fmt.Println(finalResultSetOld)
		// t2 = time.Now()
		// fmt.Println("GetResultParquet", col, t2.Sub(t1))

		// t1 = time.Now()

		// getResultLDB(col, searchString)
		// //fmt.Println(finalResultSetOld)
		// t2 = time.Now()

		// fmt.Println("GetResultLDB ", col, t2.Sub(t1))

	}
	file.Close()
}

func getResult(col, searchString string) {
	offset := getOffset(col, searchString)
	offsetMap := map[string]bool{}
	for _, val := range offset {
		offsetMap[val] = true

	}
	for _, val := range getColList {
		file, err := os.Open("/Users/<USER>/workspace/indexing_poc/existing/" + val + "_ex.txt")
		if err != nil {
			fmt.Println("Error opening file:", err)

		}

		// Create a new CSV reader with a custom comma (field separator)
		reader := csv.NewReader(file)
		reader.Comma = '^' // Set the field separator to semicolon (;)

		// Read all records from the CSV file
		records, err := reader.ReadAll()
		if err != nil {
			fmt.Println("Error reading CSV:", err)
		}

		// Print each record
		for _, record := range records {

			value := strings.TrimSuffix(record[1], "\n")
			if _, ok := offsetMap[record[0]]; ok {
				fmt.Println(value)
			}

		}
		file.Close()
	}
}

func getResultExisting(col, searchString string) {
	offset := getOffsetExisting(col, searchString)

	for _, val := range getColList {
		index := 0

		file, err := os.Open("/Users/<USER>/workspace/indexing_poc/existing/" + val + "_ex.txt")
		if err != nil {
			fmt.Println("Error opening file:", err)

		}
		defer file.Close()

		buffer := make([]byte, 1024) // Read 1 byte at a time

		// Read the file byte by byte until EOF (end of file)

		// Read a byte into the buffer
		_, err = file.Read(buffer)

		if err != nil {
			fmt.Println("file end")
		}
		lineAndLen := 4
		start := 0
		var lineNumber, valueLen, valueBuffer []byte
		for {

			start, lineNumber, buffer = getBytes(start, lineAndLen, buffer, file)
			if start == 0 {
				break
			}
			start, valueLen, buffer = getBytes(start, lineAndLen, buffer, file)
			if start == 0 {
				break
			}

			line := int32(binary.BigEndian.Uint32(lineNumber))
			vLen := int32(binary.BigEndian.Uint32(valueLen))
			if int(vLen) == 0 {
				break
			}
			if index >= len(offset) {
				break
			}
			if offset[index] == line {
				index = index + 1
				start, valueBuffer, buffer = getBytes(start, int(vLen), buffer, file)

				if j != 0 {
					fmt.Println(string(valueBuffer))
				}

				if start == 0 {
					break

				}

				continue
			}
			start = start + int(vLen)

		}
		file.Close()

		// Create a buffer to store the bytes read
	}
}

func getResultParquet(col, searchString string) {
	offsets := getOffsetParquet(col, searchString)
	for _, val := range getColList {

		fr, err := local.NewLocalFileReader("/Users/<USER>/workspace/indexing_poc/get/" + val + "_pq.parquet")
		if err != nil {
			log.Println("Can't open file")
			return
		}

		pr, err := reader.NewParquetReader(fr, new(fileio.Precord), 4)
		if err != nil {
			log.Println("Can't create parquet reader", err)
			return
		}
		n := pr.GetNumRows()
		var i int64
		for i = 0; i < n; i = i + 100 {
			record := make([]fileio.Precord, 100)
			if err = pr.Read(&record); err != nil {
				log.Println("Read error", err)
				break
			}
			for _, val := range record {
				if _, ok := offsets[val.LineNumber]; ok {
					if j != 0 {
						fmt.Println(val.Value)
					}
				}
			}

		}

		pr.ReadStop()
		fr.Close()
	}
}
func getOffsetParquet(col, searchString string) map[int32]bool {
	offsets := make(map[int32]bool)
	fr, err := local.NewLocalFileReader("/Users/<USER>/workspace/indexing_poc/get/" + col + "_pq.parquet")
	if err != nil {
		fmt.Println("Error opening file:", err)

	}
	defer fr.Close()

	pr, err := reader.NewParquetReader(fr, new(fileio.Precord), 4)
	if err != nil {
		log.Println("Can't create parquet reader", err)
		return offsets
	}

	n := int(pr.GetNumRows())
	for i := 0; i <= n; i = i + 100 {

		var record1 []fileio.Precord
		record1 = make([]fileio.Precord, 100)
		if err = pr.Read(&record1); err != nil {
			log.Println("Read error", err)
			break
		}
		for _, val := range record1 {
			if val.Value == searchString {
				offsets[val.LineNumber] = true
			}
		}

	}
	return offsets
}

func getResultInx(col, searchString string) {
	offset := getOffsetInx(col, searchString)
	for _, val := range getColList {

		file, err := os.Open("/Users/<USER>/workspace/indexing_poc/search/" + val + "_inx.txt")
		if err != nil {
			fmt.Println("Error opening file:", err)

		}
		defer file.Close()
		startByteOffsetBuff := make([]byte, 4)
		_, err = file.Read(startByteOffsetBuff)
		if err != nil {
			fmt.Println("file end")
			return
		}
		startOffset := int64(binary.BigEndian.Uint32(startByteOffsetBuff))

		file.Seek(startOffset, io.SeekStart)

		byteValue := make([]byte, 4)

		_, err = file.Read(byteValue)
		if err != nil {
			// Check if it's EOF
			if err.Error() == "EOF" {
				break
			} else {
				fmt.Println("Error:", err)
				return
			}
		}
		indexBlock := binary.BigEndian.Uint32(byteValue)
		indexBlockBuffer := make([]byte, indexBlock*12)
		_, err = file.Read(indexBlockBuffer)

		if err != nil {
			// Check if it's EOF
			if err.Error() == "EOF" {
				break
			} else {
				fmt.Println("Error:", err)
				return
			}
		}
		requiredOffsets := getRequiredIndex(indexBlockBuffer, offset)

		var bytesToRead, minOffset uint32
		if len(requiredOffsets) == 1 {
			minOffset = requiredOffsets[0].offset
			bytesToRead = requiredOffsets[0].len

		} else if len(requiredOffsets) > 1 {
			minOffset = requiredOffsets[0].offset
			maxOffset := requiredOffsets[len(requiredOffsets)-1].offset + requiredOffsets[len(requiredOffsets)-1].len
			bytesToRead = maxOffset - minOffset
		}

		resultBuffer := make([]byte, bytesToRead)
		// temp, err := file.Seek(0, io.SeekCurrent)
		// minOffset = minOffset-uint32(temp)
		file.Seek(int64(minOffset), io.SeekStart)
		file.Read(resultBuffer)
		for _, val := range requiredOffsets {
			i := val.offset - minOffset
			valueBuff := resultBuffer[i : i+val.len]
			if j != 0 {
				fmt.Println(string(valueBuff))
			}

		}
	}

}
func getRequiredIndex(inxBlock []byte, offset []uint32) []offsetAndLen {
	var result []offsetAndLen
	for _, val := range offset {

		offsetValue, vLen := binarySearch(inxBlock, val)
		var temp offsetAndLen
		temp.offset = offsetValue
		temp.len = vLen
		result = append(result, temp)
	}
	return result

}

func binarySearch(arr []byte, x uint32) (uint32, uint32) {

	low := 0
	//high := len(arr) - 1
	high1 := len(arr) / 12
	//fmt.Println(high1)
	for low <= high1 {
		mid := (low + high1) / 2
		mid1 := mid * 12
		value := binary.BigEndian.Uint32(arr[mid1 : mid1+4])
		if value == x {
			return binary.BigEndian.Uint32(arr[mid1+4 : mid1+8]), binary.BigEndian.Uint32(arr[mid1+8 : mid1+12])
		} else if value < x {
			low = mid
		} else {
			high1 = mid
		}
	}

	return 0, 0 // Element not found
}

func getBytesBuffer(start, bytesToRead int, buffer *bytes.Buffer, file *os.File) (int, []byte, *bytes.Buffer) {
	var byteData []byte
	var end int
	if start >= buffer.Len() {
		start = start - buffer.Len()

		newBuffer := make([]byte, 1024)
		file.Seek(int64(start), io.SeekCurrent)
		start = 0
		end = start + bytesToRead
		_, err := file.Read(newBuffer)

		if err != nil {
			return 0, byteData, buffer
		}
		buffer = bytes.NewBuffer(newBuffer)
		byteData = append(byteData, buffer.Next(end)...)
		return end, byteData, buffer
	}

	end = start + bytesToRead
	if end > buffer.Len() {
		byteData = buffer.Next(buffer.Len() - start)
		end = end - buffer.Len()
		for {
			if end > 1024 {
				start = 0
				newBuffer := make([]byte, 1024)
				_, err := file.Read(newBuffer)
				if err != nil {
					return 0, byteData, buffer
				}
				byteData = append(byteData, newBuffer...)
				end = end - len(newBuffer)
			} else {
				break
			}
		}
		start = 0
		newBuffer := make([]byte, 1024)
		_, err := file.Read(newBuffer)
		if err != nil {
			return 0, byteData, buffer
		}
		byteData = append(byteData, newBuffer[start:end]...)
		buffer = bytes.NewBuffer(newBuffer)
		return end, byteData, buffer
	}

	byteData = buffer.Next(end)
	return end, byteData, buffer

}

func getBytes(start, bytesToRead int, buffer []byte, file *os.File) (int, []byte, []byte) {
	var byteData []byte
	var end int
	if start >= len(buffer) {
		start = start - len(buffer)

		//var newBuffer []byte
		newBuffer := make([]byte, 1024)
		file.Seek(int64(start), io.SeekCurrent)
		start = 0
		end = start + bytesToRead
		_, err := file.Read(newBuffer)
		if err != nil {

			return 0, byteData, buffer
		}
		buffer = newBuffer
		byteData = append(byteData, buffer[start:end]...)
		return end, byteData, buffer

	}
	end = start + bytesToRead
	if end > len(buffer) {
		byteData = buffer[start:len(buffer)]
		end = end - len(buffer)
		for {

			if end > 1024 {
				start = 0
				newBuffer := make([]byte, 1024)
				_, err := file.Read(newBuffer)
				if err != nil {
					fmt.Println("file end")
					return 0, byteData, buffer
				}

				byteData = append(byteData, newBuffer...)

				end = end - len(newBuffer)
			} else {

				break
			}
		}
		start = 0
		newBuffer := make([]byte, 1024)
		_, err := file.Read(newBuffer)
		if err != nil {
			fmt.Println("file end")
			return 0, byteData, buffer
		}

		byteData = append(byteData, newBuffer[start:end]...)

		buffer = newBuffer

		return end, byteData, buffer

	}
	byteData = buffer[start:end]

	return end, byteData, buffer

}

func getOffsetExisting(col, searchString string) []int32 {
	var offsets []int32
	file, err := os.Open("/Users/<USER>/workspace/indexing_poc/existing/" + col + "_ex.txt")
	if err != nil {
		fmt.Println("Error opening file:", err)

	}
	defer file.Close()

	buffer := make([]byte, 1024) // Read 1 byte at a time

	// Read the file byte by byte until EOF (end of file)

	// Read a byte into the buffer
	_, err = file.Read(buffer)

	if err != nil {
		fmt.Println("file end")
		return offsets
	}
	lineAndLen := 4
	start := 0
	var lineNumber, valueLen, valueBuffer []byte
	for {

		start, lineNumber, buffer = getBytes(start, lineAndLen, buffer, file)
		if start == 0 {
			break
		}
		start, valueLen, buffer = getBytes(start, lineAndLen, buffer, file)
		if start == 0 {
			break
		}

		line := int32(binary.BigEndian.Uint32(lineNumber))
		vLen := int32(binary.BigEndian.Uint32(valueLen))
		if int(vLen) == 0 {
			break
		}
		start, valueBuffer, buffer = getBytes(start, int(vLen), buffer, file)

		if start == 0 {
			break
		}

		if strings.EqualFold(searchString, string(valueBuffer)) == true {

			offsets = append(offsets, line)

			// Process the byte (in this example, just print it)
			//fmt.Printf("%d %s\n", line, string(valueBuffer))
		}

	}
	return offsets

}

func getOffsetInx(col, searchString string) []uint32 {
	searchStringLen := len(searchString)
	var offsets []uint32
	file, err := os.Open("/Users/<USER>/workspace/indexing_poc/search/" + col + "_inx.txt")
	if err != nil {
		fmt.Println("Error opening file:", err)

	}
	defer file.Close()

	startByteOffsetBuff := make([]byte, 4)
	_, err = file.Read(startByteOffsetBuff)
	if err != nil {
		fmt.Println("file end")
		return offsets
	}

	//startOffset := int64(binary.BigEndian.Uint32(startByteOffsetBuff))
	//.Seek(startOffset-1, io.SeekStart)
	buffer := make([]byte, 1024) // Read 1 byte at a time
	// Read the file byte by byte until EOF (end of file)

	// Read a byte into the
	_, err = file.Read(buffer)

	if err != nil {
		fmt.Println("file end")
		return offsets
	}
	lineAndLen := 8
	start := 0

	var lineNumber, valueBuffer []byte
	for {

		start, lineNumber, buffer = getBytes(start, lineAndLen, buffer, file)
		if start == 0 {
			break
		}

		vLen := int32(binary.BigEndian.Uint32(lineNumber[0:4]))
		listLen := int32(binary.BigEndian.Uint32(lineNumber[4:8]))

		if int(vLen) == 0 {
			break
		}
		if vLen != int32(searchStringLen) {
			start = start + int(listLen*4) + int(vLen)
			continue
		}
		start, valueBuffer, buffer = getBytes(start, int(vLen), buffer, file)
		if start == 0 {
			break
		}

		if strings.EqualFold(searchString, string(valueBuffer)) == true {
			start, valueBuffer, buffer = getBytes(start, int(listLen*4), buffer, file)
			for i := 0; i < len(valueBuffer)-4; i = i + 4 {
				offsets = append(offsets, binary.BigEndian.Uint32(valueBuffer[i:i+4]))
			}
			return offsets
			// Process the byte (in this example, just print it)
			//fmt.Printf("%d %s\n", line, string(valueBuffer))
		} else {
			start = start + int(listLen*4)
		}

	}
	return offsets

}

func getOffset(col, searchString string) []string {
	var offsets []string
	file, err := os.Open("/Users/<USER>/workspace/indexing_poc/existing/" + col + "_ex.txt")
	if err != nil {
		fmt.Println("Error opening file:", err)

	}

	// Create a new CSV reader with a custom comma (field separator)
	reader := csv.NewReader(file)
	reader.Comma = '^' // Set the field separator to semicolon (;)

	// Read all records from the CSV file
	records, err := reader.ReadAll()
	if err != nil {
		fmt.Println("Error reading CSV:", err)
	}

	// Print each record
	for _, record := range records {
		value := strings.TrimSuffix(record[1], "\n")
		if value == searchString {
			offsets = append(offsets, record[0])
		}
	}
	file.Close()
	return offsets

}

func getResultNew(col, searchString string) {
	offsets, _ := searchstring(col, searchString)
	getData(offsets, col, searchString)
	//getDataPb(offsets, col, searchString)
	//getDataLDB(offsets, col, searchString)

}

func getResultLDB(col, searchString string) {
	offsets, _ := searchstring(col, searchString)
	//getData(offsets, col, searchString)
	//getDataPb(offsets, col, searchString)
	getDataLDB(offsets, col, searchString)

}

func searchstring(col string, searchString string) ([]int, error) {
	var empty []int
	var f fileio.FileIo
	f.FileName = col + "_.gob"
	f.Ctype = "search"

	// Read the map back from the file
	loadedMap, err := f.LoadMapFromFileSearch()
	if err != nil {
		fmt.Println("Error loading map from file:", err)
		return empty, err
	}

	// Print the loaded map
	//fmt.Println("Loaded map:", loadedMap[searchString])
	return loadedMap[searchString], nil
}

func getData(offsets []int, col, searchString string) []string {

	var f fileio.FileIo
	f.Ctype = "get"
	var result []string

	for _, colName := range getColList {
		f.FileName = colName + "_.gob"

		// Read the map back from the file
		loadedMap, err := f.LoadMapFromFileGet()
		if err != nil {
			fmt.Println("Error loading map from file:", err)
		}

		result = getVal(offsets, loadedMap)
		//fmt.Println(result)

	}
	return result
}

func getDataLDB(offsets []int, col, searchString string) []string {

	var f fileio.FileIo
	f.Ctype = "get"
	var result []string
	for _, colName := range getColList {
		f.FileName = colName

		// Read the map back from the file
		db, err := f.LoadMapFromFileGetLDB()
		if err != nil {
			fmt.Println("Error loading map from file:", err)
		}

		result = getValLDB(offsets, db)

		//fmt.Println(result)
		db.Close()

	}
	return result
}

// func getDataPb(offsets []int, col, searchString string) {

// 	var f fileio.FileIo
// 	f.Ctype = "get"
// 	for _, colName := range getColList {
// 		f.FileName = colName + "_.pb"

// 		// Read the map back from the file
// 		loadedMap, err := f.LoadMapFromFileGetProto()
// 		if err != nil {
// 			fmt.Println("Error loading map from file:", err)
// 		}
// 		result := getVal(offsets, loadedMap)

// 		fmt.Println(result)

// 	}
// }

func getValLDB(offsets []int, db *leveldb.DB) []string {
	var result []string

	for _, val := range offsets {

		value, err := db.Get([]byte(strconv.Itoa(int(val))), nil)
		if err != nil {
			log.Fatal(err)
		} //result = append(result, coldata[int32(val)])
		result = append(result, string(value))
	}

	return result

}

func getVal(offsets []int, coldata map[int32]string) []string {

	var result []string

	for _, val := range offsets {

		//fmt.Print(val)
		result = append(result, coldata[int32(val)])
	}

	return result
}
