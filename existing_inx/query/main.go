package main

import (
	"bytes"
	"encoding/binary"
	"encoding/csv"
	"fmt"

	// fileio "index_poc/file_io"
	"io"
	// "log"
	// "strconv"
	"strings"
	"time"

	"os"
	// "github.com/syndtr/goleveldb/leveldb"
	// "github.com/xitongsys/parquet-go-source/local"
	// "github.com/xitongsys/parquet-go/reader"
)

var getColList = []string{"timestamp", "source_ip", "destination_ip", "destination_location", "message"}
var j = 0

type offsetAndLen struct {
	offset uint32
	len    uint32
}

type finalResult struct {
	timestamp            string
	source_ip            string
	destination_ip       string
	destination_location string
}

func main() {
	// Open the CSV file
	file, err := os.Open("/Users/<USER>/workspace/ultrafast_standalone/existing_inx/search_string.txt")
	if err != nil {
		fmt.Println("Error opening file:", err)
		return
	}
	defer file.Close()

	// Create a new CSV reader with a custom comma (field separator)
	reader := csv.NewReader(file)
	reader.Comma = '=' // Set the field separator to semicolon (;)

	// Read all records from the CSV file
	records, err := reader.ReadAll()
	if err != nil {
		fmt.Println("Error reading CSV:", err)
		return
	}

	// Print each record
	for _, record := range records {
		col := record[0]
		searchString := record[1]
		t1 := time.Now()

		// getResultNew(col, searchString)
		// t2 := time.Now()
		// fmt.Println("GetResultNew ", col, t2.Sub(t1))

		getResultExisting(col, searchString)
		//fmt.Println(finalResultSetOld)
		t2 := time.Now()
		fmt.Println("GetResultExisting ", col, t2.Sub(t1))

		t1 = time.Now()
		getResultInx(col, searchString)
		t2 = time.Now()
		fmt.Println("GetResultInx ", col, t2.Sub(t1))

		// t1 = time.Now()

		// getResultParquet(col, searchString)
		// //fmt.Println(finalResultSetOld)
		// t2 = time.Now()
		// fmt.Println("GetResultParquet", col, t2.Sub(t1))

		// t1 = time.Now()

		// getResultLDB(col, searchString)
		// //fmt.Println(finalResultSetOld)
		// t2 = time.Now()

		// fmt.Println("GetResultLDB ", col, t2.Sub(t1))

	}
	file.Close()
}

// func getResult(col, searchString string) {
// 	offset := getOffset(col, searchString)
// 	offsetMap := map[string]bool{}
// 	for _, val := range offset {
// 		offsetMap[val] = true

// 	}
// 	for _, val := range getColList {
// 		file, err := os.Open("/Users/<USER>/workspace/indexing_poc/existing/" + val + "_ex.txt")
// 		if err != nil {
// 			fmt.Println("Error opening file:", err)

// 		}

// 		// Create a new CSV reader with a custom comma (field separator)
// 		reader := csv.NewReader(file)
// 		reader.Comma = '^' // Set the field separator to semicolon (;)

// 		// Read all records from the CSV file
// 		records, err := reader.ReadAll()
// 		if err != nil {
// 			fmt.Println("Error reading CSV:", err)
// 		}

// 		// Print each record
// 		for _, record := range records {

// 			value := strings.TrimSuffix(record[1], "\n")
// 			if _, ok := offsetMap[record[0]]; ok {
// 				fmt.Println(value)
// 			}

// 		}
// 		file.Close()
// 	}
// }

func getResultExisting(col, searchString string) {
	offset := getOffsetExisting(col, searchString)

	for _, val := range getColList {
		index := 0

		file, err := os.Open("/Users/<USER>/workspace/ultrafast_standalone/existing_inx/existing/" + val + "_ex.txt")
		if err != nil {
			fmt.Println("Error opening file:", err)

		}
		defer file.Close()

		buffer := make([]byte, 8192) // Read 1 byte at a time

		// Read the file byte by byte until EOF (end of file)

		// Read a byte into the buffer
		_, err = file.Read(buffer)

		if err != nil {
			fmt.Println("file end")
		}
		lineAndLen := 4
		start := 0
		var lineNumber, valueLen, valueBuffer []byte
		for {

			start, lineNumber, buffer = getBytes(start, lineAndLen, buffer, file)
			if start == 0 {
				break
			}
			start, valueLen, buffer = getBytes(start, lineAndLen, buffer, file)
			if start == 0 {
				break
			}

			line := int32(binary.BigEndian.Uint32(lineNumber))
			vLen := int32(binary.BigEndian.Uint32(valueLen))
			if int(vLen) == 0 {
				break
			}
			if index >= len(offset) {
				break
			}
			if offset[index] == line {
				index = index + 1
				start, valueBuffer, buffer = getBytes(start, int(vLen), buffer, file)

				if j != 0 {
					fmt.Println(string(valueBuffer))
				}

				if start == 0 {
					break

				}

				continue
			}
			start = start + int(vLen)

		}
		file.Close()

		// Create a buffer to store the bytes read
	}
}

func getResultInx(col, searchString string) {
	offset := getOffsetInx(col, searchString)
	for _, val := range getColList {

		file, err := os.Open("/Users/<USER>/workspace/ultrafast_standalone/existing_inx/existing/search/" + val + "_inx_com.txt")
		if err != nil {
			fmt.Println("Error opening file:", err)

		}
		defer file.Close()
		startByteOffsetBuff := make([]byte, 8)
		_, err = file.Read(startByteOffsetBuff)
		if err != nil {
			fmt.Println("file end")
			return
		}
		startOffset := int64(binary.BigEndian.Uint32(startByteOffsetBuff[0:4]))
		endOffset := int64(binary.BigEndian.Uint32(startByteOffsetBuff[4:8]))

		file.Seek(int64(startOffset), io.SeekStart)
		buffer := make([]byte, 4)
		_, err = file.Read(buffer)
		if err != nil {
			fmt.Println("Error reading file:", err)
			return
		}

		indexBlock := binary.BigEndian.Uint32(buffer)
		indexBlockBuffer := make([]byte, indexBlock*8)
		_, err = file.Read(indexBlockBuffer)
		if err != nil {
			// Check if it's EOF
			if err.Error() == "EOF" {
				break
			} else {
				fmt.Println("Error:", err)
				return
			}
		}
		//valueOffsetStart := uint32(startOffset) + 4 + indexBlock*8

		requiredOffsets, _, _ := getRequiredIndex(indexBlockBuffer, offset)
		file.Seek(8, io.SeekStart)
		resultBuffer := make([]byte, endOffset-8)
		_, err = file.Read(resultBuffer)
		for i := 0; i < len(requiredOffsets); {
			val := requiredOffsets[i]
			i = i + 1
			baseOffset := uint32(val.offset - 8)

			// fmt.Println(val.offset, val.len)
			// fmt.Println(baseOffset, minOffset, val.offset)
			valueLenOff := baseOffset + 4

			valueLenBuff := resultBuffer[baseOffset:valueLenOff]
			valueLen := binary.BigEndian.Uint32(valueLenBuff)
			valueBuff := resultBuffer[baseOffset+8 : baseOffset+8+valueLen]
			if j != 0 {
				fmt.Println(string(valueBuff))
			}

		}
	}

}

func getRequiredIndex(inxBlock []byte, offset []byte) ([]offsetAndLen, uint32, uint32) {
	var result []offsetAndLen
	maxOffset := uint32(0)
	minOffset := uint32(999999999)
	if (len(offset) / 4) <= ((len(inxBlock) / 8) / 3) {

		for i := 0; i < len(offset); i = i + 4 {

			offsetValue, _ := binarySearch(inxBlock, offset[i:i+4])
			var temp offsetAndLen
			temp.offset = offsetValue
			if temp.offset > maxOffset {
				maxOffset = temp.offset
			}
			if temp.offset < minOffset {
				minOffset = temp.offset
			}
			result = append(result, temp)
		}

		return result, maxOffset, minOffset

	}

	minLineNumber := offset[0:4]
	maxLineNumber := offset[len(offset)-4:]

	minOff, _ := binarySearch(inxBlock, minLineNumber)
	maxOff, _ := binarySearch(inxBlock, maxLineNumber)
	var temp offsetAndLen
	temp.offset = minOff
	if temp.offset > maxOffset {
		maxOffset = temp.offset
	}
	if temp.offset < minOffset {
		minOffset = temp.offset

	}
	result = append(result, temp)
	k := 4
	for i := 8; i < len(inxBlock)-8; i = i + 8 {
		//fmt.Println(i, len(inxBlock), j)
		if k+4 > len(offset) {
			break
		}

		if bytes.Equal(inxBlock[i:i+4], offset[k:k+4]) == true {
			temp.offset = binary.BigEndian.Uint32(inxBlock[i+4 : i+8])
			if temp.offset > maxOffset {
				maxOffset = temp.offset
			}
			if temp.offset < minOffset {
				minOffset = temp.offset

			}
			result = append(result, temp)
			k = k + 4
		}
	}
	temp.offset = maxOff
	if temp.offset > maxOffset {
		maxOffset = temp.offset
	}
	if temp.offset < minOffset {
		minOffset = temp.offset

	}
	result = append(result, temp)
	return result, maxOffset, minOffset
}

func binarySearch(arr []byte, x []byte) (uint32, uint32) {
	searchVal := binary.BigEndian.Uint32(x)
	low := 0
	//high := len(arr) - 1
	high1 := len(arr) / 8
	//fmt.Println(high1)
	for low <= high1 {
		mid := (low + high1) / 2
		mid1 := mid * 8
		value := binary.BigEndian.Uint32(arr[mid1 : mid1+4])
		if value == searchVal {
			return binary.BigEndian.Uint32(arr[mid1+4 : mid1+8]), uint32(mid1)
		} else if value < searchVal {
			low = mid
		} else {
			high1 = mid
		}
	}

	return 0, 0 // Element not found
}

// func getBytesBuffer(start, bytesToRead int, buffer *bytes.Buffer, file *os.File) (int, []byte, *bytes.Buffer) {
// 	var byteData []byte
// 	var end int
// 	if start >= buffer.Len() {
// 		start = start - buffer.Len()

// 		newBuffer := make([]byte, 8192)
// 		file.Seek(int64(start), io.SeekCurrent)
// 		start = 0
// 		end = start + bytesToRead
// 		_, err := file.Read(newBuffer)

// 		if err != nil {
// 			return 0, byteData, buffer
// 		}
// 		buffer = bytes.NewBuffer(newBuffer)
// 		byteData = append(byteData, buffer.Next(end)...)
// 		return end, byteData, buffer
// 	}

// 	end = start + bytesToRead
// 	if end > buffer.Len() {
// 		byteData = buffer.Next(buffer.Len() - start)
// 		end = end - buffer.Len()
// 		for {
// 			if end > 8192 {
// 				start = 0
// 				newBuffer := make([]byte, 8192)
// 				_, err := file.Read(newBuffer)
// 				if err != nil {
// 					return 0, byteData, buffer
// 				}
// 				byteData = append(byteData, newBuffer...)
// 				end = end - len(newBuffer)
// 			} else {
// 				break
// 			}
// 		}
// 		start = 0
// 		newBuffer := make([]byte, 0, 8192)
// 		_, err := file.Read(newBuffer)
// 		if err != nil {
// 			return 0, byteData, buffer
// 		}
// 		byteData = append(byteData, newBuffer[start:end]...)
// 		buffer = bytes.NewBuffer(newBuffer)
// 		return end, byteData, buffer
// 	}

// 	byteData = buffer.Next(end)
// 	return end, byteData, buffer

// }

func getBytes(start, bytesToRead int, buffer []byte, file *os.File) (int, []byte, []byte) {
	var byteData []byte
	var end int

	if start >= len(buffer) {
		start = start - len(buffer)

		file.Seek(int64(start), io.SeekCurrent)
		start = 0
		end = start + bytesToRead
		newBuffer := make([]byte, 8192)
		_, err := file.Read(newBuffer)
		if err != nil {
			return 0, byteData, buffer
		}
		byteData = append(byteData, newBuffer[start:end]...)
		buffer = newBuffer
		return end, byteData, buffer
	}
	end = start + bytesToRead
	if end > len(buffer) {
		byteData = buffer[start:len(buffer)]
		end = end - len(buffer)
		for {

			if end > 8192 {
				start = 0
				newBuffer := make([]byte, 8192)

				_, err := file.Read(newBuffer)

				if err != nil {
					fmt.Println("file end", err)
					return 0, byteData, buffer
				}

				byteData = append(byteData, newBuffer...)

				end = end - len(buffer)
			} else {

				break
			}
		}
		start = 0
		newBuffer := make([]byte, 8192)
		//buffer = buffer[:8192]
		_, err := file.Read(newBuffer)
		if err != nil {
			return 0, byteData, buffer
		}

		byteData = append(byteData, newBuffer[start:end]...)

		buffer = newBuffer

		return end, byteData, buffer

	}
	byteData = buffer[start:end]

	return end, byteData, buffer

}

func getOffsetExisting(col, searchString string) []int32 {
	var offsets []int32
	file, err := os.Open("/Users/<USER>/workspace/indexing_poc/existing/" + col + "_ex.txt")
	if err != nil {
		fmt.Println("Error opening file:", err)

	}
	defer file.Close()

	buffer := make([]byte, 8192) // Read 1 byte at a time

	// Read the file byte by byte until EOF (end of file)

	// Read a byte into the buffer
	_, err = file.Read(buffer)

	if err != nil {
		fmt.Println("file end")
		return offsets
	}
	lineAndLen := 4
	start := 0
	var lineNumber, valueLen, valueBuffer []byte
	for {

		start, lineNumber, buffer = getBytes(start, lineAndLen, buffer, file)
		if start == 0 {
			break
		}
		start, valueLen, buffer = getBytes(start, lineAndLen, buffer, file)
		if start == 0 {
			break
		}

		line := int32(binary.BigEndian.Uint32(lineNumber))
		vLen := int32(binary.BigEndian.Uint32(valueLen))
		if int(vLen) == 0 {
			break
		}

		start, valueBuffer, buffer = getBytes(start, int(vLen), buffer, file)

		if start == 0 {
			break
		}

		if strings.EqualFold(searchString, string(valueBuffer)) == true {

			offsets = append(offsets, line)

			// Process the byte (in this example, just print it)
			//fmt.Printf("%d %s\n", line, string(valueBuffer))
		}

	}
	return offsets

}

func getOffsetInx(col, searchString string) []byte {
	var offsets []byte
	file, err := os.Open("/Users/<USER>/workspace/ultrafast_standalone/existing_inx/existing/search/" + col + "_inx_com.txt")
	if err != nil {
		fmt.Println("Error opening file:", err)

	}
	defer file.Close()
	startByteOffsetBuff := make([]byte, 8)
	_, err = file.Read(startByteOffsetBuff)
	if err != nil {
		fmt.Println("file end")
		return offsets
	}
	//Read first 8 magic bytes to get start offsets of blocks
	startoffsetIndexBlock := binary.BigEndian.Uint32((startByteOffsetBuff[4:8]))
	// fmt.Println(endoffsetIndexBlock)
	// fmt.Println(startoffsetIndexBlock)

	//Substract 8 bytes from offset to bytes to read Uniq values list
	//with offset of linenumber array.
	// <8 byte><<This block has uniq value list>><index block>
	// So Start offset of Index block - 8 bytes = Total byte len of uniq value list
	uniqValueListBlockSize := startoffsetIndexBlock - 8

	LineListOffset := getListOffset(searchString, file, int(uniqValueListBlockSize))
	if LineListOffset == 0 {
		fmt.Println("No data found for Search String: ", searchString)
		return offsets
	}

	file.Seek(int64(LineListOffset), io.SeekStart)
	buffer := make([]byte, 4)
	_, err = file.Read(buffer)
	if err != nil {
		fmt.Println("Error reading file:", err)
		return offsets
	}
	// Read the file byte by byte
	bytesToRead := binary.BigEndian.Uint32(buffer) * 4
	buffer = make([]byte, bytesToRead)
	_, err = file.Read(buffer)
	if err != nil {
		fmt.Println("Error reading file:", err)
		return offsets
	}

	return buffer

}

// func getByteBlock(file *os.File, byteToRead int) ([]byte, int) {
// 	byteBlock := make([]byte, byteToRead)
// 	n, err := file.Read(byteBlock)
// 	if err != nil {
// 		fmt.Println("file end")
// 		return byteBlock, -1
// 	}

// 	return byteBlock, n
// }

func getListOffset(searchString string, file *os.File, bytesToRead int) uint32 {
	serachStringLen := len(searchString)
	bufferSize := 8192
	// if Uniq values block size less than default buffer size
	// then set buffer size to Uniq values block size

	if bytesToRead > bufferSize {
		bufferSize = bytesToRead
	}

	buffer := make([]byte, bufferSize)
	_, err := file.Read(buffer)
	if err != nil {
		fmt.Println("Error reading file:", err)
		return 0
	}
	for i := 0; i < len(buffer); {
		vLen := binary.BigEndian.Uint32(buffer[i : i+4])
		if vLen == uint32(serachStringLen) {
			listOffset := binary.BigEndian.Uint32(buffer[i+4 : i+8])
			value := buffer[i+8 : i+8+int(vLen)]
			if string(value) == searchString {
				return listOffset
			}
		}
		i += 8 + int(vLen)
	}

	return 0
}

// func getOffset(col, searchString string) []string {
// 	var offsets []string
// 	file, err := os.Open("/Users/<USER>/workspace/indexing_poc/existing/" + col + "_ex.txt")
// 	if err != nil {
// 		fmt.Println("Error opening file:", err)

// 	}

// 	// Create a new CSV reader with a custom comma (field separator)
// 	reader := csv.NewReader(file)
// 	reader.Comma = '^' // Set the field separator to semicolon (;)

// 	// Read all records from the CSV file
// 	records, err := reader.ReadAll()
// 	if err != nil {
// 		fmt.Println("Error reading CSV:", err)
// 	}

// 	// Print each record
// 	for _, record := range records {
// 		value := strings.TrimSuffix(record[1], "\n")
// 		if value == searchString {
// 			offsets = append(offsets, record[0])
// 		}
// 	}
// 	file.Close()
// 	return offsets

// }

// func getResultNew(col, searchString string) {
// 	offsets, _ := searchstring(col, searchString)
// 	getData(offsets, col, searchString)
// 	//getDataPb(offsets, col, searchString)
// 	//getDataLDB(offsets, col, searchString)

// }

// func getResultLDB(col, searchString string) {
// 	offsets, _ := searchstring(col, searchString)
// 	//getData(offsets, col, searchString)
// 	//getDataPb(offsets, col, searchString)
// 	getDataLDB(offsets, col, searchString)

// }

// func searchstring(col string, searchString string) ([]int, error) {
// 	var empty []int
// 	var f fileio.FileIo
// 	f.FileName = col + "_.gob"
// 	f.Ctype = "search"

// 	// Read the map back from the file
// 	loadedMap, err := f.LoadMapFromFileSearch()
// 	if err != nil {
// 		fmt.Println("Error loading map from file:", err)
// 		return empty, err
// 	}

// 	// Print the loaded map
// 	//fmt.Println("Loaded map:", loadedMap[searchString])
// 	return loadedMap[searchString], nil
// }

// func getData(offsets []int, col, searchString string) []string {

// 	var f fileio.FileIo
// 	f.Ctype = "get"
// 	var result []string

// 	for _, colName := range getColList {
// 		f.FileName = colName + "_.gob"

// 		// Read the map back from the file
// 		loadedMap, err := f.LoadMapFromFileGet()
// 		if err != nil {
// 			fmt.Println("Error loading map from file:", err)
// 		}

// 		result = getVal(offsets, loadedMap)
// 		//fmt.Println(result)

// 	}
// 	return result
// }

// func getDataLDB(offsets []int, col, searchString string) []string {

// 	var f fileio.FileIo
// 	f.Ctype = "get"
// 	var result []string
// 	for _, colName := range getColList {
// 		f.FileName = colName

// 		// Read the map back from the file
// 		db, err := f.LoadMapFromFileGetLDB()
// 		if err != nil {
// 			fmt.Println("Error loading map from file:", err)
// 		}

// 		result = getValLDB(offsets, db)

// 		//fmt.Println(result)
// 		db.Close()

// 	}
// 	return result
// }

// // func getDataPb(offsets []int, col, searchString string) {

// // 	var f fileio.FileIo
// // 	f.Ctype = "get"
// // 	for _, colName := range getColList {
// // 		f.FileName = colName + "_.pb"

// // 		// Read the map back from the file
// // 		loadedMap, err := f.LoadMapFromFileGetProto()
// // 		if err != nil {
// // 			fmt.Println("Error loading map from file:", err)
// // 		}
// // 		result := getVal(offsets, loadedMap)

// // 		fmt.Println(result)

// // 	}
// // }

// func getValLDB(offsets []int, db *leveldb.DB) []string {
// 	var result []string

// 	for _, val := range offsets {

// 		value, err := db.Get([]byte(strconv.Itoa(int(val))), nil)
// 		if err != nil {
// 			log.Fatal(err)
// 		} //result = append(result, coldata[int32(val)])
// 		result = append(result, string(value))
// 	}

// 	return result

// }

// func getVal(offsets []int, coldata map[int32]string) []string {

// 	var result []string

// 	for _, val := range offsets {

// 		//fmt.Print(val)
// 		result = append(result, coldata[int32(val)])
// 	}

// 	return result
// }
