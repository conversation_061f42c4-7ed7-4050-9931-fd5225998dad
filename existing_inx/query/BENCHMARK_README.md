# Enhanced Indexing Benchmark Suite

## 🎯 Overview

This enhanced benchmark suite provides comprehensive performance testing for three different indexing approaches:

1. **Existing** - Current binary format implementation (`getResultExisting`)
2. **ExistingInx** - Current compressed index implementation (`getResultInx`) 
3. **UltraFast** - New memory-mapped implementation with perfect hashing

## 🚀 Quick Start

### Option 1: Run Go Benchmarks (Recommended)
```bash
# Run comprehensive benchmarks with detailed analysis
go run . benchmark

# Run specific benchmark categories
go test -bench=BenchmarkGetResultExisting -benchmem
go test -bench=BenchmarkGetResultExistingInx -benchmem  
go test -bench=BenchmarkGetResultUltraFast -benchmem
go test -bench=BenchmarkCompareAllApproaches -benchmem
```

### Option 2: Run Shell Script
```bash
# Run complete benchmark suite with automated analysis
./run_comprehensive_benchmarks.sh
```

## 📊 Benchmark Categories

### 1. **Complete Result Retrieval Benchmarks**
- `BenchmarkGetResultExisting` - End-to-end performance using existing approach
- `BenchmarkGetResultExistingInx` - End-to-end performance using existing index approach
- `BenchmarkGetResultUltraFast` - End-to-end performance using UltraFast approach

**What it tests:** Full query processing including:
- Finding matching line numbers for the filter column
- Retrieving complete rows with selected columns from original data
- Simulates real-world usage patterns

### 2. **Search-Only Benchmarks**
- `BenchmarkSearchOnly` - Tests only the search/lookup operation
- Measures time to find matching line numbers without result retrieval

### 3. **Comparative Analysis**
- `BenchmarkCompareAllApproaches` - Side-by-side comparison of all approaches
- `BenchmarkMemoryUsage` - Memory allocation and GC impact analysis

### 4. **Correctness Validation**
- `TestResultCorrectness` - Ensures all approaches return identical results

## 🔍 Query Configuration

The benchmarks use queries from `../search_string.txt`:
```
source_username=odjordjevicrp
protocol=TCP
```

**Selected Columns Returned:** `timestamp`, `source_ip`, `destination_ip`, `destination_location`, `message`

## 📈 Understanding Results

### Benchmark Output Format
```
BenchmarkGetResultExisting/Existing_protocol_TCP-8    100    12345678 ns/op    4096 B/op    25 allocs/op
```

- **100**: Number of iterations run
- **12345678 ns/op**: Nanoseconds per operation (lower = faster)
- **4096 B/op**: Bytes allocated per operation (lower = more memory efficient)
- **25 allocs/op**: Number of allocations per operation (lower = less GC pressure)

### Performance Comparison
```bash
# Compare search performance across approaches
go test -bench="BenchmarkSearchOnly" -benchmem

# Compare complete retrieval performance  
go test -bench="BenchmarkGetResult.*" -benchmem

# Memory usage analysis
go test -bench="BenchmarkMemoryUsage" -benchmem -memprofile=mem.prof
```

## 🎯 Expected Performance Characteristics

Based on the implementation analysis:

| Approach | Search Time | Memory Usage | Best For |
|----------|-------------|--------------|----------|
| **Existing** | ~2-3ms | High | Compatibility |
| **ExistingInx** | ~400-500µs | Medium | Balanced performance |
| **UltraFast** | ~80-100µs | Low | Maximum performance |

## 🔧 Implementation Details

### Test Structure
```go
type TestResult struct {
    LineNumber uint32
    Columns    map[string]string  // Selected columns as key-value pairs
}
```

### Query Processing Flow
1. **Search Phase**: Find line numbers matching filter criteria
2. **Retrieval Phase**: Get complete rows for matching line numbers
3. **Selection Phase**: Extract only the selected columns
4. **Result Assembly**: Return structured results

### File Format Integration
- **Existing**: Reads from `../existing/{column}_ex.txt` binary files
- **ExistingInx**: Reads from `../search/{column}_inx_com.txt` compressed index files
- **UltraFast**: Reads from `../benchmark/demo_indexes/{column}_ultrafast.ufidx` memory-mapped files

## 📁 Output Files

When running benchmarks, results are saved to timestamped directories:

```
benchmark_results_YYYYMMDD_HHMMSS/
├── search_performance.csv           # Search-only benchmark results
├── retrieval_performance.csv        # Complete retrieval benchmark results  
├── memory_analysis.txt              # Memory usage analysis
├── correctness_validation.txt       # Result validation report
└── comprehensive_report.md          # Executive summary
```

## 🧪 Running Specific Tests

```bash
# Test only correctness validation
go test -run="TestResultCorrectness" -v

# Benchmark specific approach
go test -bench="BenchmarkGetResultUltraFast" -benchmem -count=3

# Profile memory usage
go test -bench="BenchmarkMemoryUsage" -benchmem -memprofile=mem.prof

# CPU profiling
go test -bench="BenchmarkCompareAllApproaches" -cpuprofile=cpu.prof
```

## 🎉 Key Benefits

1. **Comprehensive Testing**: Tests both search and complete result retrieval
2. **Real-World Simulation**: Uses actual query patterns from search_string.txt
3. **Multiple Metrics**: Measures latency, memory usage, and allocation patterns
4. **Correctness Validation**: Ensures all approaches return identical results
5. **Automated Analysis**: Generates detailed performance reports
6. **Easy Integration**: Works with existing codebase and file formats

## 🔍 Troubleshooting

### Common Issues
1. **File Not Found**: Ensure `../search_string.txt` exists with proper format
2. **Index Files Missing**: Run benchmark generation first to create index files
3. **Permission Errors**: Ensure write permissions for results directory

### Debug Mode
```bash
# Run with verbose output
go test -bench="BenchmarkGetResult.*" -benchmem -v

# Check file paths and permissions
ls -la ../search_string.txt
ls -la ../existing/
ls -la ../search/
```

This enhanced benchmark suite provides the comprehensive testing framework you requested, comparing all three approaches with complete result retrieval functionality.
