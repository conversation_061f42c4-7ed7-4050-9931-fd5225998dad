#!/bin/bash

# Comprehensive Benchmark Runner for Indexing POC
# This script runs all benchmark tests and generates detailed performance reports

set -e

echo "🚀 Starting Comprehensive Indexing Benchmark Suite"
echo "=================================================="

# Create results directory
RESULTS_DIR="benchmark_results_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$RESULTS_DIR"

echo "📁 Results will be saved to: $RESULTS_DIR"

# Function to run benchmark and save results
run_benchmark() {
    local test_name="$1"
    local output_file="$2"
    
    echo "🔄 Running $test_name..."
    go test -bench="$test_name" -benchmem -count=3 -timeout=30m > "$RESULTS_DIR/$output_file" 2>&1
    
    if [ $? -eq 0 ]; then
        echo "✅ $test_name completed successfully"
    else
        echo "❌ $test_name failed - check $output_file for details"
    fi
}

# 1. Test result correctness first
echo ""
echo "🧪 Testing Result Correctness"
echo "-----------------------------"
go test -run="TestResultCorrectness" -v > "$RESULTS_DIR/correctness_test.log" 2>&1

# 2. Run search-only benchmarks (fastest operations)
echo ""
echo "🔍 Running Search-Only Benchmarks"
echo "---------------------------------"
run_benchmark "BenchmarkSearchOnly" "search_only_benchmarks.txt"

# 3. Run complete result retrieval benchmarks
echo ""
echo "📊 Running Complete Result Retrieval Benchmarks"
echo "-----------------------------------------------"
run_benchmark "BenchmarkGetResultExisting" "existing_complete_benchmarks.txt"
run_benchmark "BenchmarkGetResultExistingInx" "existing_inx_complete_benchmarks.txt"
run_benchmark "BenchmarkGetResultUltraFast" "ultrafast_complete_benchmarks.txt"

# 4. Run comparative benchmarks
echo ""
echo "⚖️  Running Comparative Benchmarks"
echo "----------------------------------"
run_benchmark "BenchmarkCompareAllApproaches" "comparative_benchmarks.txt"

# 5. Run memory usage benchmarks
echo ""
echo "💾 Running Memory Usage Benchmarks"
echo "----------------------------------"
run_benchmark "BenchmarkMemoryUsage" "memory_benchmarks.txt"

# 6. Run legacy benchmarks for compatibility
echo ""
echo "🔄 Running Legacy Benchmarks"
echo "----------------------------"
run_benchmark "BenchmarkGetResultExiting" "legacy_existing_benchmarks.txt"
run_benchmark "BenchmarkGetResultInx" "legacy_inx_benchmarks.txt"

# Generate summary report
echo ""
echo "📈 Generating Summary Report"
echo "---------------------------"

cat > "$RESULTS_DIR/benchmark_summary.md" << 'EOF'
# Comprehensive Indexing Benchmark Results

## 🎯 Test Overview

This report contains comprehensive benchmark results comparing three different indexing approaches:

1. **Existing** - Current binary format implementation
2. **ExistingInx** - Current compressed index implementation  
3. **UltraFast** - New memory-mapped implementation with perfect hashing

## 📊 Test Categories

### 1. Search-Only Performance
- Tests only the search/lookup operation
- Measures time to find matching line numbers
- Does not include result retrieval overhead

### 2. Complete Result Retrieval
- Tests end-to-end performance including result retrieval
- Measures time to find matches AND retrieve selected columns
- Simulates real-world usage patterns

### 3. Memory Usage Analysis
- Reports memory allocations and garbage collection impact
- Identifies memory efficiency of each approach
- Helps optimize for memory-constrained environments

### 4. Correctness Validation
- Ensures all approaches return identical results
- Validates data integrity across different formats
- Confirms implementation correctness

## 📁 File Structure

```
benchmark_results_YYYYMMDD_HHMMSS/
├── search_only_benchmarks.txt          # Search operation benchmarks
├── existing_complete_benchmarks.txt    # Complete retrieval - Existing
├── existing_inx_complete_benchmarks.txt # Complete retrieval - ExistingInx  
├── ultrafast_complete_benchmarks.txt   # Complete retrieval - UltraFast
├── comparative_benchmarks.txt          # Side-by-side comparison
├── memory_benchmarks.txt               # Memory usage analysis
├── correctness_test.log                # Result validation
└── benchmark_summary.md                # This summary report
```

## 🔍 How to Interpret Results

### Benchmark Output Format
```
BenchmarkName-8    1000000    1234 ns/op    456 B/op    7 allocs/op
```

- **1000000**: Number of iterations run
- **1234 ns/op**: Nanoseconds per operation (lower is better)
- **456 B/op**: Bytes allocated per operation (lower is better)  
- **7 allocs/op**: Number of allocations per operation (lower is better)

### Performance Comparison
- Compare ns/op values to see which approach is fastest
- Compare B/op and allocs/op to see which is most memory efficient
- Look for consistent results across multiple runs (count=3)

## 🎯 Key Metrics to Focus On

1. **Latency**: ns/op for response time analysis
2. **Memory Efficiency**: B/op for memory usage optimization
3. **Allocation Pressure**: allocs/op for GC impact assessment
4. **Consistency**: Standard deviation across runs for reliability

EOF

echo "✅ Benchmark suite completed successfully!"
echo ""
echo "📋 Summary:"
echo "  - Results saved to: $RESULTS_DIR"
echo "  - View summary: cat $RESULTS_DIR/benchmark_summary.md"
echo "  - Check correctness: cat $RESULTS_DIR/correctness_test.log"
echo ""
echo "🔍 Quick Analysis Commands:"
echo "  # View search-only performance:"
echo "  grep 'ns/op' $RESULTS_DIR/search_only_benchmarks.txt"
echo ""
echo "  # Compare complete retrieval performance:"
echo "  grep 'ns/op' $RESULTS_DIR/*complete_benchmarks.txt"
echo ""
echo "  # Check memory usage:"
echo "  grep 'allocs/op' $RESULTS_DIR/memory_benchmarks.txt"
echo ""
echo "🎉 Benchmark analysis complete!"
