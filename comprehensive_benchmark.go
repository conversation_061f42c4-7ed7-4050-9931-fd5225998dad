package main

import (
	"fmt"
	"os"
	"runtime"
	"time"
)

// ComprehensiveBenchmark manages end-to-end benchmarking of all implementations
type ComprehensiveBenchmark struct {
	csvFile     string
	outputBase  string
	tableName   string
	queries     []BenchmarkQuery
}

// NewComprehensiveBenchmark creates a new comprehensive benchmark
func NewComprehensiveBenchmark(csvFile, outputBase, tableName string) *ComprehensiveBenchmark {
	return &ComprehensiveBenchmark{
		csvFile:    csvFile,
		outputBase: outputBase,
		tableName:  tableName,
		queries:    getDefaultBenchmarkQueries(),
	}
}

// RunFullBenchmark executes a complete benchmark including generation and querying
func (b *ComprehensiveBenchmark) RunFullBenchmark() (*FullBenchmarkResult, error) {
	fmt.Println("🚀 Starting Comprehensive Benchmark Suite")
	fmt.Println("="*80)
	
	startTime := time.Now()
	
	// Get system info
	systemInfo := b.getSystemInfo()
	fmt.Printf("💻 System: %s\n", systemInfo.String())
	fmt.Printf("📁 CSV File: %s\n", b.csvFile)
	fmt.Printf("📂 Output Base: %s\n", b.outputBase)
	fmt.Printf("🏷️  Table Name: %s\n", b.tableName)
	fmt.Printf("🔍 Benchmark Queries: %d\n\n", len(b.queries))

	result := &FullBenchmarkResult{
		SystemInfo:  systemInfo,
		StartTime:   startTime,
		CSVFile:     b.csvFile,
		TableName:   b.tableName,
		QueryCount:  len(b.queries),
	}

	// Phase 1: Index Generation
	fmt.Println("📊 PHASE 1: Index Generation")
	fmt.Println("-"*50)
	
	generationResult, err := b.runGenerationBenchmark()
	if err != nil {
		return nil, fmt.Errorf("generation benchmark failed: %v", err)
	}
	result.GenerationResult = generationResult
	
	// Phase 2: Query Performance
	fmt.Println("\n🔍 PHASE 2: Query Performance")
	fmt.Println("-"*50)
	
	queryResult, err := b.runQueryBenchmark()
	if err != nil {
		return nil, fmt.Errorf("query benchmark failed: %v", err)
	}
	result.QueryResult = queryResult

	// Phase 3: Storage Analysis
	fmt.Println("\n💾 PHASE 3: Storage Analysis")
	fmt.Println("-"*50)
	
	storageResult := b.runStorageAnalysis(generationResult)
	result.StorageResult = storageResult

	result.TotalTime = time.Since(startTime)
	
	fmt.Printf("\n🎉 Comprehensive benchmark completed in %v\n", result.TotalTime)
	return result, nil
}

// runGenerationBenchmark benchmarks index generation performance
func (b *ComprehensiveBenchmark) runGenerationBenchmark() (*UnifiedGenerationResult, error) {
	generator := NewUnifiedDataGenerator(b.csvFile, b.outputBase, b.tableName)
	return generator.GenerateAllIndexes()
}

// runQueryBenchmark benchmarks query performance
func (b *ComprehensiveBenchmark) runQueryBenchmark() (*BenchmarkResult, error) {
	queryEngine := NewUnifiedQueryEngine(b.outputBase)
	if err := queryEngine.Initialize(); err != nil {
		return nil, err
	}
	defer queryEngine.Close()

	return queryEngine.ExecuteBenchmarkQueries(b.queries)
}

// runStorageAnalysis analyzes storage efficiency
func (b *ComprehensiveBenchmark) runStorageAnalysis(genResult *UnifiedGenerationResult) *StorageAnalysisResult {
	result := &StorageAnalysisResult{
		Implementations: make(map[string]*StorageStats),
	}

	for name, implResult := range genResult.Implementations {
		if !implResult.Success {
			continue
		}

		stats := &StorageStats{
			Name:              name,
			TotalSize:         implResult.TotalSize,
			RecordCount:       implResult.TotalRecords,
			UniqueValueCount:  implResult.TotalUniqueValues,
			CompressionRatio:  b.calculateCompressionRatio(implResult),
			SizePerRecord:     float64(implResult.TotalSize) / float64(implResult.TotalRecords),
			Features:          implResult.Features,
		}

		result.Implementations[name] = stats
		
		if result.SmallestSize == 0 || implResult.TotalSize < result.SmallestSize {
			result.SmallestSize = implResult.TotalSize
			result.MostEfficient = name
		}
	}

	return result
}

// calculateCompressionRatio estimates compression ratio
func (b *ComprehensiveBenchmark) calculateCompressionRatio(implResult *ImplementationResult) float64 {
	// Estimate raw data size (rough calculation)
	estimatedRawSize := float64(implResult.TotalRecords) * 50.0 // Assume 50 bytes per record on average
	if estimatedRawSize > 0 {
		return estimatedRawSize / float64(implResult.TotalSize)
	}
	return 1.0
}

// getSystemInfo collects system information
func (b *ComprehensiveBenchmark) getSystemInfo() *SystemInfo {
	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)

	return &SystemInfo{
		OS:           runtime.GOOS,
		Architecture: runtime.GOARCH,
		CPUs:         runtime.NumCPU(),
		GoVersion:    runtime.Version(),
		MemoryMB:     memStats.Sys / 1024 / 1024,
	}
}

// getDefaultBenchmarkQueries returns a set of default benchmark queries
func getDefaultBenchmarkQueries() []BenchmarkQuery {
	return []BenchmarkQuery{
		{Column: "protocol", Operator: "=", Value: "TCP"},
		{Column: "protocol", Operator: "=", Value: "UDP"},
		{Column: "action", Operator: "=", Value: "ALLOW"},
		{Column: "action", Operator: "=", Value: "DENY"},
		{Column: "rule_name", Operator: "=", Value: "firewall_rule_1"},
		{Column: "source_username", Operator: "=", Value: "john_doe"},
		{Column: "destination_port", Operator: "=", Value: "80"},
		{Column: "destination_port", Operator: "=", Value: "443"},
		{Column: "source_country", Operator: "=", Value: "US"},
		{Column: "destination_country", Operator: "=", Value: "CA"},
	}
}

// FullBenchmarkResult contains comprehensive benchmark results
type FullBenchmarkResult struct {
	SystemInfo       *SystemInfo
	StartTime        time.Time
	TotalTime        time.Duration
	CSVFile          string
	TableName        string
	QueryCount       int
	GenerationResult *UnifiedGenerationResult
	QueryResult      *BenchmarkResult
	StorageResult    *StorageAnalysisResult
}

// SystemInfo contains system information
type SystemInfo struct {
	OS           string
	Architecture string
	CPUs         int
	GoVersion    string
	MemoryMB     uint64
}

// String returns a formatted system info string
func (s *SystemInfo) String() string {
	return fmt.Sprintf("%s/%s, %d CPUs, %d MB RAM, %s", 
		s.OS, s.Architecture, s.CPUs, s.MemoryMB, s.GoVersion)
}

// StorageAnalysisResult contains storage efficiency analysis
type StorageAnalysisResult struct {
	Implementations map[string]*StorageStats
	MostEfficient   string
	SmallestSize    int64
}

// StorageStats contains storage statistics for an implementation
type StorageStats struct {
	Name              string
	TotalSize         int64
	RecordCount       uint32
	UniqueValueCount  uint32
	CompressionRatio  float64
	SizePerRecord     float64
	Features          []string
}

// PrintComprehensiveReport prints a detailed benchmark report
func (r *FullBenchmarkResult) PrintComprehensiveReport() {
	fmt.Println("\n" + "="*100)
	fmt.Println("📊 COMPREHENSIVE BENCHMARK REPORT")
	fmt.Println("="*100)

	// System Information
	fmt.Printf("💻 System: %s\n", r.SystemInfo.String())
	fmt.Printf("📁 Dataset: %s\n", r.CSVFile)
	fmt.Printf("🏷️  Table: %s\n", r.TableName)
	fmt.Printf("⏱️  Total Time: %v\n", r.TotalTime.Round(time.Millisecond))
	fmt.Printf("🔍 Queries: %d\n\n", r.QueryCount)

	// Generation Performance
	fmt.Println("🔧 INDEX GENERATION PERFORMANCE")
	fmt.Println("-"*100)
	fmt.Printf("%-25s | %12s | %10s | %12s | %15s\n", 
		"Implementation", "Build Time", "Size", "Records", "Features")
	fmt.Println("-"*100)

	for name, impl := range r.GenerationResult.Implementations {
		if impl.Success {
			fmt.Printf("%-25s | %12v | %10s | %12d | %15d\n",
				name,
				impl.BuildTime.Round(time.Millisecond),
				formatBytes(impl.TotalSize),
				impl.TotalRecords,
				len(impl.Features))
		} else {
			fmt.Printf("%-25s | %12s | %10s | %12s | %15s\n",
				name, "FAILED", "N/A", "N/A", "N/A")
		}
	}

	// Query Performance
	fmt.Println("\n🔍 QUERY PERFORMANCE")
	fmt.Println("-"*100)
	fmt.Printf("%-25s | %12s | %12s | %12s | %10s | %8s\n",
		"Implementation", "Avg Time", "Min Time", "Max Time", "Success", "QPS")
	fmt.Println("-"*100)

	for name, engine := range r.QueryResult.EngineResults {
		if engine.SuccessfulQueries > 0 {
			fmt.Printf("%-25s | %12v | %12v | %12v | %10d | %8.1f\n",
				name,
				engine.AverageTime.Round(time.Microsecond),
				engine.MinTime.Round(time.Microsecond),
				engine.MaxTime.Round(time.Microsecond),
				engine.SuccessfulQueries,
				engine.QueriesPerSecond)
		} else {
			fmt.Printf("%-25s | %12s | %12s | %12s | %10d | %8s\n",
				name, "N/A", "N/A", "N/A", 0, "N/A")
		}
	}

	// Storage Efficiency
	fmt.Println("\n💾 STORAGE EFFICIENCY")
	fmt.Println("-"*100)
	fmt.Printf("%-25s | %12s | %15s | %12s | %15s\n",
		"Implementation", "Total Size", "Size/Record", "Compression", "Efficiency")
	fmt.Println("-"*100)

	for name, storage := range r.StorageResult.Implementations {
		efficiency := float64(r.StorageResult.SmallestSize) / float64(storage.TotalSize) * 100
		fmt.Printf("%-25s | %12s | %15.2f B | %12.2fx | %14.1f%%\n",
			name,
			formatBytes(storage.TotalSize),
			storage.SizePerRecord,
			storage.CompressionRatio,
			efficiency)
	}

	// Performance Summary
	fmt.Println("\n🏆 PERFORMANCE SUMMARY")
	fmt.Println("-"*100)

	// Find fastest for generation and query
	var fastestGen, fastestQuery string
	var fastestGenTime, fastestQueryTime time.Duration = time.Hour, time.Hour

	for name, impl := range r.GenerationResult.Implementations {
		if impl.Success && impl.BuildTime < fastestGenTime {
			fastestGenTime = impl.BuildTime
			fastestGen = name
		}
	}

	for name, engine := range r.QueryResult.EngineResults {
		if engine.SuccessfulQueries > 0 && engine.AverageTime < fastestQueryTime {
			fastestQueryTime = engine.AverageTime
			fastestQuery = name
		}
	}

	fmt.Printf("🥇 Fastest Generation: %s (%v)\n", fastestGen, fastestGenTime.Round(time.Millisecond))
	fmt.Printf("🥇 Fastest Query: %s (%v avg)\n", fastestQuery, fastestQueryTime.Round(time.Microsecond))
	fmt.Printf("🥇 Most Storage Efficient: %s (%s)\n", 
		r.StorageResult.MostEfficient, formatBytes(r.StorageResult.SmallestSize))

	fmt.Println("\n" + "="*100)
}

// SaveReport saves the benchmark report to a file
func (r *FullBenchmarkResult) SaveReport(filename string) error {
	file, err := os.Create(filename)
	if err != nil {
		return err
	}
	defer file.Close()

	// Redirect stdout to file temporarily
	oldStdout := os.Stdout
	os.Stdout = file
	
	r.PrintComprehensiveReport()
	
	// Restore stdout
	os.Stdout = oldStdout
	
	fmt.Printf("📄 Report saved to: %s\n", filename)
	return nil
}

// RunComprehensiveBenchmark is a convenience function to run a full benchmark
func RunComprehensiveBenchmark(csvFile, outputBase, tableName string) (*FullBenchmarkResult, error) {
	benchmark := NewComprehensiveBenchmark(csvFile, outputBase, tableName)
	return benchmark.RunFullBenchmark()
}
