package main

import (
	"encoding/binary"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"sort"
	"strings"
	"time"
)

// ExistingBinaryGenerator implements IndexGenerator for the existing binary format
type ExistingBinaryGenerator struct {
	outputDir string
}

// NewExistingBinaryGenerator creates a new generator for existing binary format
func NewExistingBinaryGenerator(outputDir string) *ExistingBinaryGenerator {
	return &ExistingBinaryGenerator{
		outputDir: outputDir,
	}
}

// GetApproach returns the indexing approach
func (g *ExistingBinaryGenerator) GetApproach() IndexingApproach {
	return ApproachExisting
}

// GetFeatures returns a list of features supported by this approach
func (g *ExistingBinaryGenerator) GetFeatures() []string {
	return []string{
		"Binary Format Storage",
		"Offset-Size-Value Structure",
		"Sequential Access",
		"Simple Implementation",
	}
}

// GenerateIndex creates an index for the given column data
func (g *ExistingBinaryGenerator) GenerateIndex(columnName string, records []Record, outputDir string) (*IndexStats, error) {
	start := time.Now()

	// Update output directory
	g.outputDir = outputDir

	// Ensure output directory exists
	if err := os.MkdirAll(g.outputDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create output directory: %v", err)
	}

	// Sort records by line number for efficient access
	sort.Slice(records, func(i, j int) bool {
		return records[i].LineNumber < records[j].LineNumber
	})

	filename := fmt.Sprintf("%s_ex.txt", columnName)
	filepath := filepath.Join(g.outputDir, filename)

	file, err := os.Create(filepath)
	if err != nil {
		return nil, fmt.Errorf("failed to create file %s: %v", filename, err)
	}

	var fileSize int64
	offsetBuffer := make([]byte, 4)
	sizeBuffer := make([]byte, 4)

	// Count unique values
	uniqueValues := make(map[string]bool)
	for _, record := range records {
		uniqueValues[record.Value] = true

		// Write offset (line number)
		binary.BigEndian.PutUint32(offsetBuffer, record.LineNumber)
		if _, err := file.Write(offsetBuffer); err != nil {
			file.Close()
			return nil, fmt.Errorf("failed to write offset: %v", err)
		}

		// Write size
		valueBytes := []byte(record.Value)
		binary.BigEndian.PutUint32(sizeBuffer, uint32(len(valueBytes)))
		if _, err := file.Write(sizeBuffer); err != nil {
			file.Close()
			return nil, fmt.Errorf("failed to write size: %v", err)
		}

		// Write value
		if _, err := file.Write(valueBytes); err != nil {
			file.Close()
			return nil, fmt.Errorf("failed to write value: %v", err)
		}

		fileSize += 8 + int64(len(valueBytes)) // 4 bytes offset + 4 bytes size + value
	}

	file.Close()

	// Get actual file size
	if stat, err := os.Stat(filepath); err == nil {
		fileSize = stat.Size()
	}

	buildTime := time.Since(start)

	return &IndexStats{
		Approach:         ApproachExisting,
		ColumnName:       columnName,
		RecordCount:      uint32(len(records)),
		UniqueValues:     uint32(len(uniqueValues)),
		FileSize:         fileSize,
		CompressionRatio: 0.0, // No compression in binary format
		BuildTime:        buildTime,
		Features:         g.GetFeatures(),
	}, nil
}

// GenerateMultiColumnIndex creates indexes for multiple columns
func (g *ExistingBinaryGenerator) GenerateMultiColumnIndex(columnData map[string][]Record, outputDir string, tableName string) (map[string]*IndexStats, error) {
	g.outputDir = outputDir
	results := make(map[string]*IndexStats)

	for columnName, records := range columnData {
		stats, err := g.GenerateIndex(columnName, records, outputDir)
		if err != nil {
			return nil, fmt.Errorf("failed to generate index for column %s: %v", columnName, err)
		}
		results[columnName] = stats
	}

	return results, nil
}

// ValidateIndex validates the integrity of an index file
func (g *ExistingBinaryGenerator) ValidateIndex(indexPath string) error {
	file, err := os.Open(indexPath)
	if err != nil {
		return fmt.Errorf("failed to open index file: %v", err)
	}
	defer file.Close()

	// Basic validation - check if file can be read in the expected format
	offsetBuffer := make([]byte, 4)
	sizeBuffer := make([]byte, 4)

	for {
		// Try to read offset
		if _, err := file.Read(offsetBuffer); err != nil {
			if err == io.EOF {
				break // End of file is expected
			}
			return fmt.Errorf("failed to read offset: %v", err)
		}

		// Try to read size
		if _, err := file.Read(sizeBuffer); err != nil {
			return fmt.Errorf("failed to read size: %v", err)
		}

		valueSize := binary.BigEndian.Uint32(sizeBuffer)

		// Skip the value
		if _, err := file.Seek(int64(valueSize), 1); err != nil {
			return fmt.Errorf("failed to skip value: %v", err)
		}
	}

	return nil
}

// GetIndexStats returns statistics about an existing index
func (g *ExistingBinaryGenerator) GetIndexStats(indexPath string) (*IndexStats, error) {
	file, err := os.Open(indexPath)
	if err != nil {
		return nil, fmt.Errorf("failed to open index file: %v", err)
	}
	defer file.Close()

	// Get file size
	fileInfo, err := file.Stat()
	if err != nil {
		return nil, err
	}

	// Count records and unique values by reading the file
	var recordCount uint32
	uniqueValues := make(map[string]bool)

	offsetBuffer := make([]byte, 4)
	sizeBuffer := make([]byte, 4)

	for {
		// Read offset
		if _, err := file.Read(offsetBuffer); err != nil {
			if err == io.EOF {
				break
			}
			return nil, err
		}

		// Read size
		if _, err := file.Read(sizeBuffer); err != nil {
			return nil, err
		}

		valueSize := binary.BigEndian.Uint32(sizeBuffer)

		// Read value
		valueBuffer := make([]byte, valueSize)
		if _, err := file.Read(valueBuffer); err != nil {
			return nil, err
		}

		uniqueValues[string(valueBuffer)] = true
		recordCount++
	}

	return &IndexStats{
		Approach:         ApproachExisting,
		RecordCount:      recordCount,
		UniqueValues:     uint32(len(uniqueValues)),
		FileSize:         fileInfo.Size(),
		CompressionRatio: 0.0,
		Features:         g.GetFeatures(),
	}, nil
}

// ExistingBinaryQueryEngine implements QueryEngine for the existing binary format
type ExistingBinaryQueryEngine struct {
	indexDir string
}

// NewExistingBinaryQueryEngine creates a new query engine for existing binary format
func NewExistingBinaryQueryEngine(indexDir string) *ExistingBinaryQueryEngine {
	return &ExistingBinaryQueryEngine{
		indexDir: indexDir,
	}
}

// GetApproach returns the indexing approach
func (e *ExistingBinaryQueryEngine) GetApproach() IndexingApproach {
	return ApproachExisting
}

// GetSupportedOperators returns supported query operators
func (e *ExistingBinaryQueryEngine) GetSupportedOperators() []string {
	return []string{"=", "!=", "LIKE", "IN"}
}

// Initialize prepares the query engine with index directory
func (e *ExistingBinaryQueryEngine) Initialize(indexDir string) error {
	e.indexDir = indexDir
	return nil
}

// ExecuteQuery executes a single filter query
func (e *ExistingBinaryQueryEngine) ExecuteQuery(columnName string, filter QueryFilter) (*QueryResult, error) {
	if filter.Operator != "=" {
		return nil, fmt.Errorf("existing binary format only supports equality queries, got: %s", filter.Operator)
	}

	start := time.Now()

	// Find matching line numbers
	matchingLines, err := e.findMatchingLines(columnName, filter.Value)
	if err != nil {
		return nil, fmt.Errorf("failed to find matching lines: %v", err)
	}

	return &QueryResult{
		LineNumbers:   matchingLines,
		ExecutionTime: time.Since(start),
		ResultCount:   len(matchingLines),
		IndexHits:     1,
	}, nil
}

// ExecuteMultiColumnQuery executes a query with multiple filters
func (e *ExistingBinaryQueryEngine) ExecuteMultiColumnQuery(filters []QueryFilter) (*QueryResult, error) {
	if len(filters) == 0 {
		return &QueryResult{}, nil
	}

	start := time.Now()
	var resultLineNumbers []uint32
	var totalIndexHits int

	// Execute first query
	firstResult, err := e.ExecuteQuery(filters[0].Column, filters[0])
	if err != nil {
		return nil, err
	}

	resultLineNumbers = firstResult.LineNumbers
	totalIndexHits += firstResult.IndexHits

	// Intersect with remaining queries
	for i := 1; i < len(filters); i++ {
		if len(resultLineNumbers) == 0 {
			break // No point continuing if no results
		}

		queryResult, err := e.ExecuteQuery(filters[i].Column, filters[i])
		if err != nil {
			return nil, err
		}

		totalIndexHits += queryResult.IndexHits

		// Intersect line numbers
		resultLineNumbers = e.intersectLineNumbers(resultLineNumbers, queryResult.LineNumbers)
	}

	return &QueryResult{
		LineNumbers:   resultLineNumbers,
		ExecutionTime: time.Since(start),
		ResultCount:   len(resultLineNumbers),
		IndexHits:     totalIndexHits,
	}, nil
}

// findMatchingLines finds line numbers that match the filter
func (e *ExistingBinaryQueryEngine) findMatchingLines(columnName string, filterValue interface{}) ([]uint32, error) {
	filename := fmt.Sprintf("%s_ex.txt", columnName)
	filepath := filepath.Join(e.indexDir, filename)

	file, err := os.Open(filepath)
	if err != nil {
		return nil, fmt.Errorf("failed to open file %s: %v", filename, err)
	}
	defer file.Close()

	var matchingLines []uint32
	searchValue := fmt.Sprintf("%v", filterValue)

	offsetBuffer := make([]byte, 4)
	sizeBuffer := make([]byte, 4)

	for {
		// Read offset (line number)
		if _, err := file.Read(offsetBuffer); err != nil {
			break // EOF
		}
		lineNumber := binary.BigEndian.Uint32(offsetBuffer)

		// Read size
		if _, err := file.Read(sizeBuffer); err != nil {
			break
		}
		valueSize := binary.BigEndian.Uint32(sizeBuffer)

		// Read value
		valueBuffer := make([]byte, valueSize)
		if _, err := file.Read(valueBuffer); err != nil {
			break
		}

		value := string(valueBuffer)
		if strings.EqualFold(searchValue, value) {
			matchingLines = append(matchingLines, lineNumber)
		}
	}

	return matchingLines, nil
}

// getCompleteRows retrieves complete rows for given line numbers
func (e *ExistingBinaryQueryEngine) getCompleteRows(lineNumbers []uint32, selectColumns []string) ([]map[string]interface{}, error) {
	if len(selectColumns) == 0 {
		// Default columns if none specified
		selectColumns = []string{"timestamp", "source_ip", "destination_ip", "destination_location", "message"}
	}

	results := make([]map[string]interface{}, 0, len(lineNumbers))

	for _, lineNumber := range lineNumbers {
		row := make(map[string]interface{})
		row["line_number"] = lineNumber

		// Get values for each selected column
		for _, columnName := range selectColumns {
			value, err := e.getValueForLine(columnName, lineNumber)
			if err != nil {
				// If column doesn't exist or error, set empty value
				row[columnName] = ""
			} else {
				row[columnName] = value
			}
		}

		results = append(results, row)
	}

	return results, nil
}

// getValueForLine gets the value for a specific column and line number
func (e *ExistingBinaryQueryEngine) getValueForLine(columnName string, targetLine uint32) (string, error) {
	filename := fmt.Sprintf("%s_ex.txt", columnName)
	filepath := filepath.Join(e.indexDir, filename)

	file, err := os.Open(filepath)
	if err != nil {
		return "", fmt.Errorf("failed to open file %s: %v", filename, err)
	}
	defer file.Close()

	offsetBuffer := make([]byte, 4)
	sizeBuffer := make([]byte, 4)

	for {
		// Read offset (line number)
		if _, err := file.Read(offsetBuffer); err != nil {
			break // EOF
		}
		lineNumber := binary.BigEndian.Uint32(offsetBuffer)

		// Read size
		if _, err := file.Read(sizeBuffer); err != nil {
			break
		}
		valueSize := binary.BigEndian.Uint32(sizeBuffer)

		if lineNumber == targetLine {
			// Found the target line, read the value
			valueBuffer := make([]byte, valueSize)
			if _, err := file.Read(valueBuffer); err != nil {
				return "", err
			}
			return string(valueBuffer), nil
		} else {
			// Skip this value
			if _, err := file.Seek(int64(valueSize), 1); err != nil {
				return "", err
			}
		}
	}

	return "", fmt.Errorf("line number %d not found in column %s", targetLine, columnName)
}

// ExecuteBatchQueries executes multiple queries and returns aggregated results
func (e *ExistingBinaryQueryEngine) ExecuteBatchQueries(queries []QueryFilter) ([]*QueryResult, error) {
	results := make([]*QueryResult, len(queries))

	for i, query := range queries {
		result, err := e.ExecuteQuery(query.Column, query)
		if err != nil {
			return nil, fmt.Errorf("batch query %d failed: %v", i, err)
		}
		results[i] = result
	}

	return results, nil
}

// intersectLineNumbers finds the intersection of two sorted line number arrays
func (e *ExistingBinaryQueryEngine) intersectLineNumbers(a, b []uint32) []uint32 {
	result := make([]uint32, 0, min(len(a), len(b)))
	i, j := 0, 0

	for i < len(a) && j < len(b) {
		if a[i] == b[j] {
			result = append(result, a[i])
			i++
			j++
		} else if a[i] < b[j] {
			i++
		} else {
			j++
		}
	}

	return result
}

// Close releases any resources
func (e *ExistingBinaryQueryEngine) Close() error {
	return nil
}
