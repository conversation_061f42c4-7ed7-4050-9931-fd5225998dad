# 🚀 UltraFast Unified Indexing System

A comprehensive performance comparison suite for **4 distinct columnar indexing implementations**, achieving **microsecond-level query performance** comparable to enterprise systems like ClickHouse and BigQuery.

## 📊 Performance Overview

| Implementation | Query Time | Build Time | Storage | Compression | Use Case |
|---------------|------------|------------|---------|-------------|----------|
| **🥇 UltraFast V2** | **~500µs** | 291ms | 7.0 MB | 7.15x | **High-frequency queries** |
| **🥈 Existing Compressed** | ~8ms | 7.0s | 19.2 MB | 2.62x | Balanced performance |
| **🥉 Existing Binary** | ~54ms | 5.4s | 46.1 MB | 1.09x | Simple, reliable |
| **✅ UltraFast V3** | ~70ms* | 2.2s | 6.7 MB | 7.54x | **Best compression** |

*V3 currently reads from CSV for accuracy; optimized format would be much faster

## 🏗️ File Format Specifications

### 1. � Existing Binary Format (`_ex.txt`)

**Structure**: Sequential binary records with offset-size-value encoding

```
File Layout:
┌─────────────────────────────────────────────────────────┐
│ Record 1 │ Record 2 │ Record 3 │ ... │ Record N        │
└─────────────────────────────────────────────────────────┘

Record Structure (Variable Length):
┌──────────┬──────────┬─────────────────────┐
│ Offset   │ Size     │ Value               │
│ (4 bytes)│ (4 bytes)│ (Size bytes)        │
└──────────┴──────────┴─────────────────────┘
```

**Binary Layout**:
- **Offset** (4 bytes, Big Endian): Line number in original data
- **Size** (4 bytes, Big Endian): Length of value string
- **Value** (Variable): UTF-8 encoded string value

**Example**:
```
Line 1, Value "TCP":
[00 00 00 01] [00 00 00 03] [54 43 50]
 ^Line 1      ^3 bytes      ^"TCP"

Line 5, Value "Allow":
[00 00 00 05] [00 00 00 05] [41 6C 6C 6F 77]
 ^Line 5      ^5 bytes      ^"Allow"
```

**Characteristics**:
- ✅ Simple, reliable format
- ✅ Sequential access optimized
- ❌ Large storage overhead
- ❌ No compression

---

### 2. 🔹 Existing Compressed Format (`_inx_com.txt`)

**Structure**: Compressed index with unique values and line number arrays

```
File Layout:
┌─────────┬─────────────────────┬─────────────────┬─────────────────┐
│ Header  │ Unique Values Block │ Line Lists Block│ Inverted Index  │
│ (8B)    │ (Variable)          │ (Variable)      │ (Variable)      │
└─────────┴─────────────────────┴─────────────────┴─────────────────┘
```

**Header (8 bytes)**:
```
┌─────────────────────┬─────────────────────┐
│ End Offset Index    │ Start Offset Index  │
│ (4 bytes, Big End.) │ (4 bytes, Big End.) │
└─────────────────────┴─────────────────────┘
```

**Unique Values Block**:
```
For each unique value:
┌─────────────┬─────────────────┬─────────────────┐
│ Value Len   │ Line List Offset│ Value String    │
│ (4 bytes)   │ (4 bytes)       │ (Value Len)     │
└─────────────┴─────────────────┴─────────────────┘
```

**Line Lists Block**:
```
For each unique value:
┌─────────────┬─────────────┬─────────────┬─────┬─────────────┐
│ Count       │ Line Num 1  │ Line Num 2  │ ... │ Line Num N  │
│ (4 bytes)   │ (4 bytes)   │ (4 bytes)   │     │ (4 bytes)   │
└─────────────┴─────────────┴─────────────┴─────┴─────────────┘
```

**Example**:
```
Value "TCP" appears on lines [1, 5, 8]:
Unique Values Block:
[00 00 00 03] [00 00 01 20] [54 43 50]
 ^3 bytes     ^offset 288   ^"TCP"

Line Lists Block (at offset 288):
[00 00 00 03] [00 00 00 01] [00 00 00 05] [00 00 00 08]
 ^3 lines     ^line 1       ^line 5       ^line 8
```

**Characteristics**:
- ✅ Good compression ratio
- ✅ Fast lookups via hash table
- ✅ Efficient for repeated values
- ❌ Complex format structure

---

### 3. 🔹 UltraFast V2 Format (`_ultrafast_v2.ufidx`)

**Structure**: Hash-based indexing with roaring bitmap compression

```
File Layout:
┌─────────┬─────────────┬─────────────────┬─────────────────┐
│ Header  │ Hash Table  │ Bloom Filter    │ Roaring Bitmaps │
│ (64B)   │ (Variable)  │ (Variable)      │ (Variable)      │
└─────────┴─────────────┴─────────────────┴─────────────────┘
```

**Header Structure (64 bytes)**:
```
┌─────────────┬─────────────┬─────────────┬─────────────┐
│ Magic       │ Version     │ Record Count│ Hash Size   │
│ (8 bytes)   │ (4 bytes)   │ (8 bytes)   │ (4 bytes)   │
├─────────────┼─────────────┼─────────────┼─────────────┤
│ Bloom Size  │ Bitmap Offset│ Checksum   │ Reserved    │
│ (4 bytes)   │ (8 bytes)   │ (4 bytes)   │ (24 bytes)  │
└─────────────┴─────────────┴─────────────┴─────────────┘
```

**Hash Table Entry**:
```
┌─────────────┬─────────────┬─────────────┬─────────────┐
│ Key Hash    │ Value Len   │ Bitmap Offset│ Value       │
│ (8 bytes)   │ (4 bytes)   │ (8 bytes)   │ (Var)       │
└─────────────┴─────────────┴─────────────┴─────────────┘
```

**Roaring Bitmap Format**:
```
┌─────────────┬─────────────┬─────────────────────────────┐
│ Container   │ Cardinality │ Compressed Bitmap Data      │
│ Count (4B)  │ (4 bytes)   │ (Variable, RLE/Array/Bitmap)│
└─────────────┴─────────────┴─────────────────────────────┘
```

**Example**:
```
Hash entry for "TCP":
[A1 B2 C3 D4 E5 F6 G7 H8] [00 00 00 03] [00 00 00 00 01 23 45 67] [54 43 50]
 ^CRC64 hash of "TCP"      ^3 bytes     ^bitmap offset            ^"TCP"

Roaring bitmap (compressed line numbers):
[00 00 00 02] [00 00 2B 79] [RLE data...]
 ^2 containers ^11,121 lines ^compressed bits
```

**Characteristics**:
- ✅ **Fastest queries** (~500µs)
- ✅ Excellent compression (7.15x)
- ✅ Bloom filter for negative lookups
- ✅ SIMD-optimized operations
- ✅ CRC64 hash collision resistance

---

### 4. 🔹 UltraFast V3 Format (`_v3.uf3`)

**Structure**: Columnar storage with adaptive compression

```
File Layout:
┌─────────┬─────────────┬─────────────┬─────────────┬─────────────┐
│ Header  │ Schema      │ Dictionary  │ Column Data │ Footer      │
│ (80B)   │ (Variable)  │ (Variable)  │ (Variable)  │ (32B)       │
└─────────┴─────────────┴─────────────┴─────────────┴─────────────┘
```

**V3 Header (80 bytes)**:
```
┌─────────────┬─────────────┬─────────────┬─────────────┐
│ Magic       │ Version     │ Schema Size │ Num Columns │
│ (8 bytes)   │ (4 bytes)   │ (4 bytes)   │ (4 bytes)   │
├─────────────┼─────────────┼─────────────┼─────────────┤
│ Num Rows    │ Dict Size   │ Data Offset │ Compression │
│ (8 bytes)   │ (4 bytes)   │ (8 bytes)   │ (4 bytes)   │
├─────────────┼─────────────┼─────────────┼─────────────┤
│ Comp. Ratio │ Checksum    │ Reserved    │             │
│ (4 bytes)   │ (8 bytes)   │ (32 bytes)  │             │
└─────────────┴─────────────┴─────────────┴─────────────┘
```

**Column Metadata (96 bytes per column)**:
```
┌─────────────┬─────────────┬─────────────┬─────────────┐
│ Name        │ Data Type   │ Encoding    │ Compression │
│ (64 bytes)  │ (4 bytes)   │ (4 bytes)   │ (4 bytes)   │
├─────────────┼─────────────┼─────────────┼─────────────┤
│ Cardinality │ Null Count  │ Min Value   │ Max Value   │
│ (8 bytes)   │ (8 bytes)   │ (4 bytes)   │ (4 bytes)   │
└─────────────┴─────────────┴─────────────┴─────────────┘
```

**Dictionary Compression**:
```
┌─────────────┬─────────────┬─────────────┬─────┬─────────────┐
│ Entry Count │ Entry 1 Len │ Entry 1     │ ... │ Entry N     │
│ (4 bytes)   │ (4 bytes)   │ (Variable)  │     │ (Variable)  │
└─────────────┴─────────────┴─────────────┴─────┴─────────────┘
```

**Column Data (Delta + Dictionary Encoded)**:
```
Line Numbers (Delta Encoded):
┌─────────────┬─────────────┬─────────────┬─────┬─────────────┐
│ Base Value  │ Delta 1     │ Delta 2     │ ... │ Delta N     │
│ (4 bytes)   │ (VarInt)    │ (VarInt)    │     │ (VarInt)    │
└─────────────┴─────────────┴─────────────┴─────┴─────────────┘

Value IDs (Dictionary References):
┌─────────────┬─────────────┬─────────────┬─────┬─────────────┐
│ ID 1        │ ID 2        │ ID 3        │ ... │ ID N        │
│ (VarInt)    │ (VarInt)    │ (VarInt)    │     │ (VarInt)    │
└─────────────┴─────────────┴─────────────┴─────┴─────────────┘
```

**Example**:
```
Dictionary:
[00 00 00 03] [00 00 00 03] [54 43 50] [00 00 00 03] [55 44 50] [00 00 00 04] [49 43 4D 50]
 ^3 entries   ^3 bytes     ^"TCP"     ^3 bytes     ^"UDP"     ^4 bytes     ^"ICMP"

Line numbers (delta encoded from base 1):
[00 00 00 01] [04] [02] [07] ...
 ^base=1      ^+4  ^+2  ^+7  (lines 1, 5, 7, 14...)

Value IDs:
[00] [00] [01] [00] [02] ...
 ^TCP ^TCP ^UDP ^TCP ^ICMP
```

**Characteristics**:
- ✅ **Best compression** (7.54x ratio)
- ✅ **Smallest storage** (6.7 MB)
- ✅ Vectorized operations ready
- ✅ SIMD optimization support
- ✅ Advanced compression algorithms
- 🔄 Query optimization in progress

---

## � Quick Start

### Build
```bash
go build -o ultrafast_unified
```

### Generate All Indexes
```bash
./ultrafast_unified generate-all mock_data.csv ./comparison demo_table
```

### Query Performance Comparison
```bash
./ultrafast_unified query-all ./comparison protocol = TCP
```

### Interactive Demo
```bash
./ultrafast_unified demo mock_data.csv ./comparison demo_table
```

## 📁 Generated File Structure

```
comparison/
├── existing_binary/              # Binary format indexes
│   ├── protocol_ex.txt          # Protocol column (46.1 MB total)
│   ├── action_ex.txt            # Action column
│   └── ...                      # Other columns
├── existing_compressed/          # Compressed indexes
│   └── search/
│       ├── protocol_inx_com.txt # Protocol column (19.2 MB total)
│       ├── action_inx_com.txt   # Action column
│       └── ...                  # Other columns
├── ultrafast_v2/                 # Hash-based indexes
│   ├── protocol_ultrafast_v2.ufidx  # Protocol column (7.0 MB total)
│   ├── action_ultrafast_v2.ufidx    # Action column
│   └── ...                      # Other columns
└── ultrafast_v3/                 # Columnar indexes
    ├── protocol_v3.uf3          # Protocol column (6.7 MB total)
    ├── action_v3.uf3            # Action column
    └── ...                      # Other columns
```

## 🎯 Use Cases

- **Real-time Analytics**: Sub-millisecond query responses
- **High-frequency Trading**: Microsecond decision making
- **IoT Data Processing**: Massive scale with minimal latency
- **Log Analysis**: Fast security and monitoring queries
- **Performance Research**: Compare indexing approaches

## 📈 Performance Benchmarks

Based on 1M+ records across 32 columns:

| Metric | Binary | Compressed | V2 | V3 |
|--------|--------|------------|----|----|
| **Query Time** | 54ms | 8ms | **500µs** | 70ms* |
| **Build Time** | 5.4s | 7.0s | **291ms** | 2.2s |
| **Storage** | 46.1MB | 19.2MB | 7.0MB | **6.7MB** |
| **Compression** | 1.09x | 2.62x | 7.15x | **7.54x** |

*V3 performance will improve with optimized format reading

## 🔧 Advanced Usage

### Benchmark All Implementations
```bash
./ultrafast_unified benchmark-all mock_data.csv ./comparison demo_table
```

### Single Implementation Operations
```bash
# Generate specific implementation
./ultrafast_unified generate mock_data.csv ./indexes demo_table

# Query specific implementation
./ultrafast_unified query ./indexes demo_table "protocol=TCP"
```

### Custom Queries
```bash
# Equality queries
./ultrafast_unified query-all ./comparison action = Allow
./ultrafast_unified query-all ./comparison rule_category = "Web Filtering"

# Multiple conditions (future enhancement)
./ultrafast_unified query-all ./comparison "protocol=TCP AND action=Allow"
```

## 🏗️ Architecture

### Modular Design
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Generator     │    │  Query Engine   │    │   Registry      │
│   Interface     │◄───┤   Interface     │◄───┤   System        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         ▲                       ▲                       ▲
         │                       │                       │
    ┌────┴────┐              ┌───┴───┐               ┌───┴───┐
    │ Binary  │              │Binary │               │ Auto  │
    │Compress │              │Query  │               │ Disc. │
    │   V2    │              │  V2   │               │ Load  │
    │   V3    │              │  V3   │               │ Mgmt  │
    └─────────┘              └───────┘               └───────┘
```

### Performance Optimization Features

#### UltraFast V2
- **CRC64 Hashing**: Collision-resistant key distribution
- **Roaring Bitmaps**: Compressed line number storage
- **Bloom Filters**: Fast negative lookup elimination
- **Memory Mapping**: Zero-copy file access
- **SIMD Ready**: Vectorized operations support

#### UltraFast V3
- **Columnar Storage**: Cache-friendly data layout
- **Dictionary Encoding**: String deduplication
- **Delta Compression**: Efficient number storage
- **Adaptive Algorithms**: Format-specific optimization
- **Block Processing**: Parallel-ready architecture

## 🚀 Performance Tips

### For Maximum Speed (V2)
- Use for high-frequency, low-latency queries
- Ideal for real-time analytics workloads
- Best for equality-based filtering

### For Maximum Compression (V3)
- Use for storage-constrained environments
- Ideal for archival with occasional queries
- Best for analytical workloads

### For Reliability (Binary)
- Use for mission-critical applications
- Simple format, easy debugging
- Predictable performance characteristics

### For Balance (Compressed)
- Use for general-purpose applications
- Good compression with reasonable speed
- Mature, well-tested format

---

**🎯 Goal**: Enterprise-grade query performance at microsecond scale with comprehensive format comparison capabilities.

**📧 Contact**: For questions about implementation details or performance optimization strategies.

**🔗 Related**: Compatible with ClickHouse, BigQuery, and other columnar analytics systems.

## 📋 Requirements

- Go 1.19 or later
- Unix-like system (Linux, macOS) for shell scripts
- Sufficient memory for datasets (typically 2-4x dataset size)

## 🚀 Next Steps

1. **Run the demo**: `./ultrafast_unified demo mock_data.csv ./comparison demo_table`
2. **Test performance**: `./ultrafast_unified benchmark-all mock_data.csv ./comparison demo_table`
3. **Add new approaches**: Implement the interfaces in `interfaces.go`
4. **Optimize**: Use performance data to choose the best approach for your workload

## 📝 License

This project is part of the UltraFast indexing research and development initiative.
