package main

import (
	"fmt"
	"path/filepath"
	"time"
)

// UnifiedQueryEngine manages queries across all implementations
type UnifiedQueryEngine struct {
	indexBase string
	engines   map[string]QueryEngine
}

// NewUnifiedQueryEngine creates a new unified query engine
func NewUnifiedQueryEngine(indexBase string) *UnifiedQueryEngine {
	return &UnifiedQueryEngine{
		indexBase: indexBase,
		engines:   make(map[string]QueryEngine),
	}
}

// Initialize sets up query engines for all available implementations
func (u *UnifiedQueryEngine) Initialize() error {
	fmt.Println("🔧 Initializing Unified Query Engine...")

	// Initialize registry
	InitializeRegistry()

	implementations := GetAllImplementations()
	for _, impl := range implementations {
		// Create index directory path for this implementation
		indexDir := filepath.Join(u.indexBase, sanitizeApproachName(string(impl.QueryEngine.GetApproach())))
		
		// Initialize the query engine
		if err := impl.QueryEngine.Initialize(indexDir); err != nil {
			fmt.Printf("⚠️  Failed to initialize %s: %v\n", impl.Name, err)
			continue
		}

		u.engines[impl.Name] = impl.QueryEngine
		fmt.Printf("✅ Initialized %s query engine\n", impl.Name)
	}

	fmt.Printf("🎉 Initialized %d query engines\n", len(u.engines))
	return nil
}

// ExecuteUnifiedQuery executes the same query across all implementations
func (u *UnifiedQueryEngine) ExecuteUnifiedQuery(columnName string, operator string, value interface{}) (*UnifiedQueryResult, error) {
	fmt.Printf("🔍 Executing unified query: %s %s %v\n", columnName, operator, value)

	filter := QueryFilter{
		Column:   columnName,
		Operator: operator,
		Value:    value,
	}

	result := &UnifiedQueryResult{
		Query:       fmt.Sprintf("%s %s %v", columnName, operator, value),
		StartTime:   time.Now(),
		Results:     make(map[string]*QueryEngineResult),
	}

	// Execute query on each engine
	for name, engine := range u.engines {
		fmt.Printf("  📊 Querying %s...", name)
		
		engineResult := &QueryEngineResult{
			EngineName: name,
			Approach:   engine.GetApproach(),
		}

		start := time.Now()
		queryResult, err := engine.ExecuteQuery(columnName, filter)
		engineResult.ExecutionTime = time.Since(start)

		if err != nil {
			engineResult.Success = false
			engineResult.Error = err.Error()
			fmt.Printf(" ❌ Error: %v\n", err)
		} else {
			engineResult.Success = true
			engineResult.ResultCount = queryResult.ResultCount
			engineResult.LineNumbers = queryResult.LineNumbers
			engineResult.IndexHits = queryResult.IndexHits
			fmt.Printf(" ✅ %d results in %v\n", queryResult.ResultCount, engineResult.ExecutionTime.Round(time.Microsecond))
		}

		result.Results[name] = engineResult
	}

	result.TotalTime = time.Since(result.StartTime)
	return result, nil
}

// ExecuteBenchmarkQueries executes a set of benchmark queries across all implementations
func (u *UnifiedQueryEngine) ExecuteBenchmarkQueries(queries []BenchmarkQuery) (*BenchmarkResult, error) {
	fmt.Printf("🏁 Starting benchmark with %d queries...\n", len(queries))

	benchResult := &BenchmarkResult{
		TotalQueries:    len(queries),
		StartTime:       time.Now(),
		EngineResults:   make(map[string]*EngineBenchmarkResult),
	}

	// Initialize engine results
	for name, engine := range u.engines {
		benchResult.EngineResults[name] = &EngineBenchmarkResult{
			EngineName:    name,
			Approach:      engine.GetApproach(),
			QueryResults:  make([]*QueryEngineResult, 0, len(queries)),
		}
	}

	// Execute each query
	for i, query := range queries {
		fmt.Printf("\n📋 Query %d/%d: %s %s %v\n", i+1, len(queries), query.Column, query.Operator, query.Value)

		unifiedResult, err := u.ExecuteUnifiedQuery(query.Column, query.Operator, query.Value)
		if err != nil {
			fmt.Printf("❌ Failed to execute query: %v\n", err)
			continue
		}

		// Add results to benchmark
		for engineName, queryResult := range unifiedResult.Results {
			benchResult.EngineResults[engineName].QueryResults = append(
				benchResult.EngineResults[engineName].QueryResults, queryResult)
		}
	}

	// Calculate statistics
	for _, engineResult := range benchResult.EngineResults {
		engineResult.calculateStatistics()
	}

	benchResult.TotalTime = time.Since(benchResult.StartTime)
	fmt.Printf("\n🎉 Benchmark completed in %v\n", benchResult.TotalTime)

	return benchResult, nil
}

// Close releases all query engine resources
func (u *UnifiedQueryEngine) Close() error {
	for name, engine := range u.engines {
		if err := engine.Close(); err != nil {
			fmt.Printf("⚠️  Failed to close %s: %v\n", name, err)
		}
	}
	return nil
}

// UnifiedQueryResult contains results from executing a query across all implementations
type UnifiedQueryResult struct {
	Query     string
	StartTime time.Time
	TotalTime time.Duration
	Results   map[string]*QueryEngineResult
}

// QueryEngineResult contains results from a single query engine
type QueryEngineResult struct {
	EngineName    string
	Approach      IndexingApproach
	Success       bool
	Error         string
	ExecutionTime time.Duration
	ResultCount   int
	LineNumbers   []uint32
	IndexHits     int
}

// BenchmarkQuery represents a query to be benchmarked
type BenchmarkQuery struct {
	Column   string
	Operator string
	Value    interface{}
}

// BenchmarkResult contains comprehensive benchmark results
type BenchmarkResult struct {
	TotalQueries  int
	StartTime     time.Time
	TotalTime     time.Duration
	EngineResults map[string]*EngineBenchmarkResult
}

// EngineBenchmarkResult contains benchmark results for a specific engine
type EngineBenchmarkResult struct {
	EngineName      string
	Approach        IndexingApproach
	QueryResults    []*QueryEngineResult
	SuccessfulQueries int
	FailedQueries   int
	TotalTime       time.Duration
	AverageTime     time.Duration
	MinTime         time.Duration
	MaxTime         time.Duration
	QueriesPerSecond float64
}

// calculateStatistics calculates benchmark statistics for an engine
func (e *EngineBenchmarkResult) calculateStatistics() {
	if len(e.QueryResults) == 0 {
		return
	}

	var totalTime time.Duration
	var minTime = time.Hour // Start with a large value
	var maxTime time.Duration

	for _, result := range e.QueryResults {
		if result.Success {
			e.SuccessfulQueries++
			totalTime += result.ExecutionTime
			
			if result.ExecutionTime < minTime {
				minTime = result.ExecutionTime
			}
			if result.ExecutionTime > maxTime {
				maxTime = result.ExecutionTime
			}
		} else {
			e.FailedQueries++
		}
	}

	e.TotalTime = totalTime
	if e.SuccessfulQueries > 0 {
		e.AverageTime = totalTime / time.Duration(e.SuccessfulQueries)
		e.MinTime = minTime
		e.MaxTime = maxTime
		e.QueriesPerSecond = float64(e.SuccessfulQueries) / totalTime.Seconds()
	}
}

// PrintSummary prints a summary of the unified query results
func (r *UnifiedQueryResult) PrintSummary() {
	fmt.Println("\n" + "="*60)
	fmt.Printf("🔍 QUERY RESULTS: %s\n", r.Query)
	fmt.Println("="*60)

	fmt.Printf("Total Execution Time: %v\n\n", r.TotalTime.Round(time.Microsecond))

	fmt.Println("Engine Performance:")
	fmt.Println("-" * 60)

	for name, result := range r.Results {
		if result.Success {
			fmt.Printf("✅ %-20s | %8v | %6d results | %d hits\n",
				name,
				result.ExecutionTime.Round(time.Microsecond),
				result.ResultCount,
				result.IndexHits)
		} else {
			fmt.Printf("❌ %-20s | Error: %s\n", name, result.Error)
		}
	}
}

// PrintBenchmarkSummary prints a comprehensive benchmark summary
func (r *BenchmarkResult) PrintBenchmarkSummary() {
	fmt.Println("\n" + "="*80)
	fmt.Println("🏁 COMPREHENSIVE BENCHMARK RESULTS")
	fmt.Println("="*80)

	fmt.Printf("Total Queries: %d\n", r.TotalQueries)
	fmt.Printf("Total Time: %v\n\n", r.TotalTime.Round(time.Millisecond))

	fmt.Println("Engine Performance Summary:")
	fmt.Println("-" * 80)
	fmt.Printf("%-20s | %8s | %8s | %8s | %8s | %6s | %6s\n",
		"Engine", "Avg Time", "Min Time", "Max Time", "Total", "Success", "QPS")
	fmt.Println("-" * 80)

	for name, result := range r.EngineResults {
		if result.SuccessfulQueries > 0 {
			fmt.Printf("%-20s | %8v | %8v | %8v | %8v | %6d | %6.1f\n",
				name,
				result.AverageTime.Round(time.Microsecond),
				result.MinTime.Round(time.Microsecond),
				result.MaxTime.Round(time.Microsecond),
				result.TotalTime.Round(time.Millisecond),
				result.SuccessfulQueries,
				result.QueriesPerSecond)
		} else {
			fmt.Printf("%-20s | %8s | %8s | %8s | %8s | %6d | %6s\n",
				name, "N/A", "N/A", "N/A", "N/A", 0, "N/A")
		}
	}

	// Performance ranking
	fmt.Println("\n🏆 Performance Ranking (by average query time):")
	fmt.Println("-" * 50)
	
	type rankEntry struct {
		name string
		avgTime time.Duration
	}
	
	var rankings []rankEntry
	for name, result := range r.EngineResults {
		if result.SuccessfulQueries > 0 {
			rankings = append(rankings, rankEntry{name, result.AverageTime})
		}
	}
	
	// Simple bubble sort for ranking
	for i := 0; i < len(rankings)-1; i++ {
		for j := 0; j < len(rankings)-i-1; j++ {
			if rankings[j].avgTime > rankings[j+1].avgTime {
				rankings[j], rankings[j+1] = rankings[j+1], rankings[j]
			}
		}
	}
	
	for i, entry := range rankings {
		medal := "🥇"
		if i == 1 { medal = "🥈" }
		if i == 2 { medal = "🥉" }
		if i > 2 { medal = "  " }
		
		fmt.Printf("%s %d. %-20s - %v\n", medal, i+1, entry.name, entry.avgTime.Round(time.Microsecond))
	}
}
