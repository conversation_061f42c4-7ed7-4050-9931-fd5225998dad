package main

import (
	"fmt"
	"sync"
)

// ImplementationRegistry manages available indexing implementations
type ImplementationRegistry struct {
	implementations map[IndexingApproach]*IndexingImplementation
	mutex           sync.RWMutex
}

// NewImplementationRegistry creates a new registry
func NewImplementationRegistry() *ImplementationRegistry {
	return &ImplementationRegistry{
		implementations: make(map[IndexingApproach]*IndexingImplementation),
	}
}

// RegisterImplementation adds a new indexing implementation
func (r *ImplementationRegistry) RegisterImplementation(impl *IndexingImplementation) error {
	if impl == nil || impl.Generator == nil || impl.QueryEngine == nil {
		return fmt.Errorf("invalid implementation: missing generator or query engine")
	}

	approach := impl.Generator.GetApproach()
	if approach != impl.QueryEngine.GetApproach() {
		return fmt.Errorf("approach mismatch between generator and query engine")
	}

	r.mutex.Lock()
	defer r.mutex.Unlock()

	if _, exists := r.implementations[approach]; exists {
		return fmt.Errorf("implementation for approach %s already registered", approach)
	}

	r.implementations[approach] = impl
	return nil
}

// GetImplementation retrieves an implementation by approach
func (r *ImplementationRegistry) GetImplementation(approach IndexingApproach) (*IndexingImplementation, error) {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	impl, exists := r.implementations[approach]
	if !exists {
		return nil, fmt.Errorf("no implementation registered for approach %s", approach)
	}

	return impl, nil
}

// GetAllImplementations returns all registered implementations
func (r *ImplementationRegistry) GetAllImplementations() []*IndexingImplementation {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	impls := make([]*IndexingImplementation, 0, len(r.implementations))
	for _, impl := range r.implementations {
		impls = append(impls, impl)
	}

	return impls
}

// GetAvailableApproaches returns list of available approach identifiers
func (r *ImplementationRegistry) GetAvailableApproaches() []IndexingApproach {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	approaches := make([]IndexingApproach, 0, len(r.implementations))
	for approach := range r.implementations {
		approaches = append(approaches, approach)
	}

	return approaches
}

// DefaultRegistry is the global registry instance
var DefaultRegistry = NewImplementationRegistry()

// RegisterImplementation is a convenience function for the default registry
func RegisterImplementation(impl *IndexingImplementation) error {
	return DefaultRegistry.RegisterImplementation(impl)
}

// GetImplementation is a convenience function for the default registry
func GetImplementation(approach IndexingApproach) (*IndexingImplementation, error) {
	return DefaultRegistry.GetImplementation(approach)
}

// GetAllImplementations is a convenience function for the default registry
func GetAllImplementations() []*IndexingImplementation {
	return DefaultRegistry.GetAllImplementations()
}

// GetAvailableApproaches is a convenience function for the default registry
func GetAvailableApproaches() []IndexingApproach {
	return DefaultRegistry.GetAvailableApproaches()
}

// InitializeRegistry registers all available implementations
func InitializeRegistry() {
	// Register Existing Binary implementation
	RegisterImplementation(&IndexingImplementation{
		Generator:   NewExistingBinaryGenerator(""),
		QueryEngine: NewExistingBinaryQueryEngine(""),
		Name:        "Existing Binary",
		Description: "Original binary format with offset-size-value structure",
		Version:     "1.0",
	})

	// Register Existing Compressed implementation
	RegisterImplementation(&IndexingImplementation{
		Generator:   NewExistingCompressedGenerator(""),
		QueryEngine: NewExistingCompressedQueryEngine(""),
		Name:        "Existing Compressed",
		Description: "Compressed index format with unique values and line lists",
		Version:     "1.1",
	})

	// Register V2 implementation
	RegisterImplementation(&IndexingImplementation{
		Generator:   NewModularV2Generator(""),
		QueryEngine: NewModularV2QueryEngine(""),
		Name:        "UltraFast V2",
		Description: "High-performance hash-based indexing with roaring bitmaps",
		Version:     "2.0",
	})

	// V3 implementation will be added later
	// RegisterImplementation(&IndexingImplementation{
	//     Generator:   NewModularV3Generator(""),
	//     QueryEngine: NewModularV3QueryEngine(""),
	//     Name:        "UltraFast V3",
	//     Description: "Columnar storage format with adaptive compression",
	//     Version:     "3.0",
	// })
}
