package main

import (
	"encoding/binary"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"time"
)

// ModularV2Generator adapts the existing V2Generator to the IndexGenerator interface
type ModularV2Generator struct {
	generator *V2Generator
}

// NewModularV2Generator creates a new modular V2 generator
func NewModularV2Generator(outputDir string) *ModularV2Generator {
	return &ModularV2Generator{
		generator: NewV2Generator(outputDir),
	}
}

// GetApproach returns the indexing approach identifier
func (g *ModularV2Generator) GetApproach() IndexingApproach {
	return ApproachV2
}

// GetFeatures returns a list of features supported by this approach
func (g *ModularV2Generator) GetFeatures() []string {
	return []string{
		"Hash Table Indexing",
		"CRC32C Hashing",
		"Roaring Bitmap Compression",
		"Bloom Filters",
		"Memory Mapping",
		"Key Prefixes",
		"Quadratic Probing",
		"SIMD Optimizations",
	}
}

// GenerateIndex creates an index for the given column data
func (g *ModularV2Generator) GenerateIndex(columnName string, records []Record, outputDir string) (*IndexStats, error) {
	start := time.Now()

	// Update the generator's output directory
	g.generator.indexDir = outputDir

	// Use the existing V2Generator
	err := g.generator.GenerateV2(columnName, records)
	if err != nil {
		return nil, err
	}

	buildTime := time.Since(start)

	// Get file stats
	filename := fmt.Sprintf("%s/%s_ultrafast_v2.ufidx", outputDir, columnName)
	fileInfo, err := os.Stat(filename)
	if err != nil {
		return nil, err
	}

	// Calculate unique values
	uniqueValues := make(map[string]bool)
	for _, record := range records {
		uniqueValues[record.Value] = true
	}

	return &IndexStats{
		Approach:         ApproachV2,
		ColumnName:       columnName,
		RecordCount:      uint32(len(records)),
		UniqueValues:     uint32(len(uniqueValues)),
		FileSize:         fileInfo.Size(),
		CompressionRatio: g.calculateCompressionRatio(records, fileInfo.Size()),
		BuildTime:        buildTime,
		Features:         g.GetFeatures(),
	}, nil
}

// GenerateMultiColumnIndex creates indexes for multiple columns
func (g *ModularV2Generator) GenerateMultiColumnIndex(columnData map[string][]Record, outputDir string, tableName string) (map[string]*IndexStats, error) {
	stats := make(map[string]*IndexStats)

	for columnName, records := range columnData {
		indexStats, err := g.GenerateIndex(columnName, records, outputDir)
		if err != nil {
			return nil, fmt.Errorf("failed to generate index for column %s: %v", columnName, err)
		}
		stats[columnName] = indexStats
	}

	return stats, nil
}

// ValidateIndex validates the integrity of an index file
func (g *ModularV2Generator) ValidateIndex(indexPath string) error {
	file, err := os.Open(indexPath)
	if err != nil {
		return err
	}
	defer file.Close()

	// Read and validate header
	header := make([]byte, 36)
	_, err = file.Read(header)
	if err != nil {
		return err
	}

	// Check magic number
	magic := string(header[:8])
	if magic != "UFIDXV2\x00" {
		return fmt.Errorf("invalid magic number: %s", magic)
	}

	return nil
}

// GetIndexStats returns statistics about an existing index
func (g *ModularV2Generator) GetIndexStats(indexPath string) (*IndexStats, error) {
	file, err := os.Open(indexPath)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	// Get file size
	fileInfo, err := file.Stat()
	if err != nil {
		return nil, err
	}

	return &IndexStats{
		Approach: ApproachV2,
		FileSize: fileInfo.Size(),
		Features: g.GetFeatures(),
	}, nil
}

// calculateCompressionRatio estimates compression ratio
func (g *ModularV2Generator) calculateCompressionRatio(records []Record, indexSize int64) float64 {
	// Estimate raw data size
	estimatedRawSize := int64(0)
	for _, record := range records {
		estimatedRawSize += int64(len(record.Value)) + 8 // value + line number
	}

	if estimatedRawSize > 0 {
		return 1.0 - float64(indexSize)/float64(estimatedRawSize)
	}

	return 0.0
}

// ModularV2QueryEngine adapts the existing V2QueryEngine to the QueryEngine interface
type ModularV2QueryEngine struct {
	engine *V2QueryEngine
}

// NewModularV2QueryEngine creates a new modular V2 query engine
func NewModularV2QueryEngine(indexDir string) *ModularV2QueryEngine {
	return &ModularV2QueryEngine{
		engine: NewV2QueryEngine(indexDir),
	}
}

// GetApproach returns the indexing approach identifier
func (e *ModularV2QueryEngine) GetApproach() IndexingApproach {
	return ApproachV2
}

// Initialize prepares the query engine with index directory
func (e *ModularV2QueryEngine) Initialize(indexDir string) error {
	e.engine.indexDir = indexDir
	return nil
}

// ExecuteQuery executes a single filter query
func (e *ModularV2QueryEngine) ExecuteQuery(columnName string, filter QueryFilter) (*QueryResult, error) {
	if filter.Operator != "=" {
		return nil, fmt.Errorf("V2 engine only supports equality queries, got: %s", filter.Operator)
	}

	searchValue, ok := filter.Value.(string)
	if !ok {
		return nil, fmt.Errorf("V2 engine only supports string values")
	}

	start := time.Now()

	// Use the existing V2QueryEngine
	lineNumbers, err := e.engine.SearchV2(columnName, searchValue)
	if err != nil {
		return nil, err
	}

	return &QueryResult{
		LineNumbers:   lineNumbers,
		ExecutionTime: time.Since(start),
		ResultCount:   len(lineNumbers),
		IndexHits:     1,
	}, nil
}

// ExecuteMultiColumnQuery executes a query with multiple filters
func (e *ModularV2QueryEngine) ExecuteMultiColumnQuery(filters []QueryFilter) (*QueryResult, error) {
	if len(filters) == 0 {
		return &QueryResult{}, nil
	}

	start := time.Now()
	var resultLineNumbers []uint32
	var totalIndexHits int

	// Execute first query
	firstResult, err := e.ExecuteQuery(filters[0].Column, filters[0])
	if err != nil {
		return nil, err
	}

	resultLineNumbers = firstResult.LineNumbers
	totalIndexHits += firstResult.IndexHits

	// Intersect with remaining queries
	for i := 1; i < len(filters); i++ {
		if len(resultLineNumbers) == 0 {
			break // No point continuing if no results
		}

		queryResult, err := e.ExecuteQuery(filters[i].Column, filters[i])
		if err != nil {
			return nil, err
		}

		totalIndexHits += queryResult.IndexHits

		// Intersect line numbers
		resultLineNumbers = e.intersectLineNumbers(resultLineNumbers, queryResult.LineNumbers)
	}

	return &QueryResult{
		LineNumbers:   resultLineNumbers,
		ExecutionTime: time.Since(start),
		ResultCount:   len(resultLineNumbers),
		IndexHits:     totalIndexHits,
	}, nil
}

// ExecuteBatchQueries executes multiple queries and returns aggregated results
func (e *ModularV2QueryEngine) ExecuteBatchQueries(queries []QueryFilter) ([]*QueryResult, error) {
	results := make([]*QueryResult, len(queries))

	for i, query := range queries {
		result, err := e.ExecuteQuery(query.Column, query)
		if err != nil {
			return nil, fmt.Errorf("batch query %d failed: %v", i, err)
		}
		results[i] = result
	}

	return results, nil
}

// GetSupportedOperators returns the list of supported query operators
func (e *ModularV2QueryEngine) GetSupportedOperators() []string {
	return []string{"="}
}

// ExecuteQueryWithRows executes a query and returns complete rows with selected columns
func (e *ModularV2QueryEngine) ExecuteQueryWithRows(columnName string, filter QueryFilter, selectColumns []string) (*QueryRowResult, error) {
	start := time.Now()

	// First execute the regular query to get line numbers
	queryResult, err := e.ExecuteQuery(columnName, filter)
	if err != nil {
		return nil, err
	}

	// Get complete rows for the matching line numbers
	rows, err := e.getCompleteRows(queryResult.LineNumbers, selectColumns)
	if err != nil {
		return nil, fmt.Errorf("failed to get complete rows: %v", err)
	}

	return &QueryRowResult{
		Rows:          rows,
		LineNumbers:   queryResult.LineNumbers,
		ExecutionTime: time.Since(start),
		ResultCount:   len(rows),
		IndexHits:     queryResult.IndexHits + len(selectColumns)*len(queryResult.LineNumbers), // Additional hits for column lookups
		ColumnsCount:  len(selectColumns),
	}, nil
}

// getCompleteRows retrieves complete rows for given line numbers
func (e *ModularV2QueryEngine) getCompleteRows(lineNumbers []uint32, selectColumns []string) ([]map[string]interface{}, error) {
	if len(selectColumns) == 0 {
		// Default columns if none specified
		selectColumns = []string{"timestamp", "source_ip", "destination_ip", "destination_location", "message"}
	}

	// Limit to first 10 rows for performance demonstration
	limitedLineNumbers := lineNumbers
	if len(lineNumbers) > 10 {
		limitedLineNumbers = lineNumbers[:10]
	}

	results := make([]map[string]interface{}, 0, len(limitedLineNumbers))

	for _, lineNumber := range limitedLineNumbers {
		row := make(map[string]interface{})
		row["line_number"] = lineNumber

		// Get values for each selected column
		for _, columnName := range selectColumns {
			value, err := e.getValueForLine(columnName, lineNumber)
			if err != nil {
				// If column doesn't exist or error, set empty value
				row[columnName] = ""
			} else {
				row[columnName] = value
			}
		}

		results = append(results, row)
	}

	return results, nil
}

// getValueForLine gets the value for a specific column and line number using V2 format
func (e *ModularV2QueryEngine) getValueForLine(columnName string, targetLine uint32) (string, error) {
	// V2 format is optimized for value-to-lines mapping, not line-to-value reverse lookup
	// For now, we'll use a simplified approach that scans all values to find the one containing our target line
	// In production, V2 would need additional reverse mapping structures

	filename := fmt.Sprintf("%s_ultrafast_v2.ufidx", columnName)
	filepath := filepath.Join(e.engine.indexDir, filename)

	file, err := os.Open(filepath)
	if err != nil {
		return "", fmt.Errorf("failed to open V2 index file %s: %v", filename, err)
	}
	defer file.Close()

	// Read V2 header
	var header V2Header
	if err := binary.Read(file, binary.LittleEndian, &header); err != nil {
		return "", fmt.Errorf("failed to read V2 header: %v", err)
	}

	// For V2 reverse lookup, we need to scan through all key entries
	// This is not optimal but works for demonstration
	// Production V2 would have dedicated reverse mapping structures

	// Skip to key directory section (after header)
	keyDirOffset := int64(binary.Size(header))
	if _, err := file.Seek(keyDirOffset, io.SeekStart); err != nil {
		return "", err
	}

	// Read key directory entries
	for i := uint32(0); i < header.NumKeys; i++ {
		var keyEntry CompressedKeyEntry
		if err := binary.Read(file, binary.LittleEndian, &keyEntry); err != nil {
			if err == io.EOF {
				break
			}
			return "", err
		}

		// Skip to data section to read the actual key and bitmap
		currentPos, _ := file.Seek(0, io.SeekCurrent)

		dataOffset := int64(keyEntry.DataOffset)
		if _, err := file.Seek(dataOffset, io.SeekStart); err != nil {
			continue
		}

		// Read key length and key
		var keyLen uint32
		if err := binary.Read(file, binary.LittleEndian, &keyLen); err != nil {
			continue
		}

		keyBytes := make([]byte, keyLen)
		if _, err := file.Read(keyBytes); err != nil {
			continue
		}
		value := string(keyBytes)

		// Read bitmap size and bitmap data
		var bitmapSize uint32
		if err := binary.Read(file, binary.LittleEndian, &bitmapSize); err != nil {
			continue
		}

		bitmapData := make([]byte, bitmapSize)
		if _, err := file.Read(bitmapData); err != nil {
			continue
		}

		// Check if this bitmap contains our target line
		// For now, use a simplified check - in production this would decode the roaring bitmap
		if e.simpleBitmapContainsLine(bitmapData, targetLine) {
			// Restore file position and return the value
			file.Seek(currentPos, io.SeekStart)
			return value, nil
		}

		// Restore file position for next iteration
		if _, err := file.Seek(currentPos, io.SeekStart); err != nil {
			return "", err
		}
	}

	return "", fmt.Errorf("line number %d not found in V2 index for column %s", targetLine, columnName)
}

// simpleBitmapContainsLine is a simplified check for line presence in bitmap
// In production, this would properly decode the roaring bitmap
func (e *ModularV2QueryEngine) simpleBitmapContainsLine(bitmapData []byte, targetLine uint32) bool {
	// This is a very simplified placeholder
	// In production, this would properly decode the roaring bitmap format
	// For now, just return true if we have bitmap data and a valid line number
	return len(bitmapData) > 0 && targetLine > 0
}

// Close releases any resources held by the query engine
func (e *ModularV2QueryEngine) Close() error {
	return e.engine.Close()
}

// intersectLineNumbers finds the intersection of two sorted line number arrays
func (e *ModularV2QueryEngine) intersectLineNumbers(a, b []uint32) []uint32 {
	result := make([]uint32, 0, min(len(a), len(b)))
	i, j := 0, 0

	for i < len(a) && j < len(b) {
		if a[i] == b[j] {
			result = append(result, a[i])
			i++
			j++
		} else if a[i] < b[j] {
			i++
		} else {
			j++
		}
	}

	return result
}

// min returns the minimum of two integers
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
