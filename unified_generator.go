package main

import (
	"encoding/csv"
	"fmt"
	"os"
	"path/filepath"
	"time"
)

// UnifiedDataGenerator manages data generation for all implementations
type UnifiedDataGenerator struct {
	csvFile    string
	outputBase string
	tableName  string
}

// NewUnifiedDataGenerator creates a new unified data generator
func NewUnifiedDataGenerator(csvFile, outputBase, tableName string) *UnifiedDataGenerator {
	return &UnifiedDataGenerator{
		csvFile:    csvFile,
		outputBase: outputBase,
		tableName:  tableName,
	}
}

// GenerateAllIndexes creates indexes for all implementations from the same CSV data
func (g *UnifiedDataGenerator) GenerateAllIndexes() (*UnifiedGenerationResult, error) {
	fmt.Println("🚀 Starting Unified Index Generation...")
	start := time.Now()

	// Read CSV data
	columnData, err := g.readCSVData()
	if err != nil {
		return nil, fmt.Errorf("failed to read CSV data: %v", err)
	}

	// Calculate total records
	totalRecords := 0
	for _, records := range columnData {
		totalRecords += len(records)
	}

	fmt.Printf("📊 Loaded %d records from %s\n", totalRecords, g.csvFile)
	fmt.Printf("📋 Processing %d columns\n", len(columnData))

	// Initialize registry with all implementations
	InitializeRegistry()

	result := &UnifiedGenerationResult{
		TableName:       g.tableName,
		TotalRecords:    totalRecords,
		ColumnCount:     len(columnData),
		Implementations: make(map[string]*ImplementationResult),
		StartTime:       start,
	}

	// Generate indexes for each implementation
	implementations := GetAllImplementations()
	for _, impl := range implementations {
		fmt.Printf("\n🔧 Generating indexes for %s...\n", impl.Name)

		implResult, err := g.generateForImplementation(impl, columnData)
		if err != nil {
			fmt.Printf("❌ Failed to generate for %s: %v\n", impl.Name, err)
			implResult = &ImplementationResult{
				Name:    impl.Name,
				Success: false,
				Error:   err.Error(),
			}
		} else {
			fmt.Printf("✅ Successfully generated indexes for %s\n", impl.Name)
		}

		result.Implementations[impl.Name] = implResult
	}

	result.TotalTime = time.Since(start)
	fmt.Printf("\n🎉 Unified generation completed in %v\n", result.TotalTime)

	return result, nil
}

// readCSVData reads and parses the CSV file and returns column-grouped data
func (g *UnifiedDataGenerator) readCSVData() (map[string][]Record, error) {
	file, err := os.Open(g.csvFile)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	reader := csv.NewReader(file)
	rows, err := reader.ReadAll()
	if err != nil {
		return nil, err
	}

	if len(rows) == 0 {
		return nil, fmt.Errorf("CSV file is empty")
	}

	// Get column names from header
	headers := rows[0]
	columnData := make(map[string][]Record)

	// Process data rows
	for lineNum, row := range rows[1:] {
		for colIndex, value := range row {
			if colIndex < len(headers) && value != "" {
				columnName := headers[colIndex]
				record := Record{
					LineNumber: uint32(lineNum + 1), // 1-based line numbers
					Value:      value,
				}
				columnData[columnName] = append(columnData[columnName], record)
			}
		}
	}

	return columnData, nil
}

// generateForImplementation generates indexes for a specific implementation
func (g *UnifiedDataGenerator) generateForImplementation(impl *IndexingImplementation, columnData map[string][]Record) (*ImplementationResult, error) {
	start := time.Now()

	// Create output directory for this implementation
	outputDir := filepath.Join(g.outputBase, sanitizeApproachName(string(impl.Generator.GetApproach())))
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create output directory: %v", err)
	}

	// Generate indexes for all columns
	columnStats, err := impl.Generator.GenerateMultiColumnIndex(columnData, outputDir, g.tableName)
	if err != nil {
		return nil, err
	}

	// Calculate totals
	var totalSize int64
	var totalRecords uint32
	var totalUniqueValues uint32

	for _, stats := range columnStats {
		totalSize += stats.FileSize
		totalRecords += stats.RecordCount
		totalUniqueValues += stats.UniqueValues
	}

	return &ImplementationResult{
		Name:              impl.Name,
		Approach:          impl.Generator.GetApproach(),
		Success:           true,
		BuildTime:         time.Since(start),
		TotalSize:         totalSize,
		TotalRecords:      totalRecords,
		TotalUniqueValues: totalUniqueValues,
		ColumnStats:       columnStats,
		OutputDirectory:   outputDir,
		Features:          impl.Generator.GetFeatures(),
	}, nil
}

// sanitizeApproachName converts approach name to filesystem-safe string
func sanitizeApproachName(approach string) string {
	// Replace spaces and special characters with underscores
	result := ""
	for _, char := range approach {
		if (char >= 'a' && char <= 'z') || (char >= 'A' && char <= 'Z') || (char >= '0' && char <= '9') {
			result += string(char)
		} else {
			result += "_"
		}
	}
	return result
}

// UnifiedGenerationResult contains results from generating all implementations
type UnifiedGenerationResult struct {
	TableName       string
	TotalRecords    int
	ColumnCount     int
	Implementations map[string]*ImplementationResult
	StartTime       time.Time
	TotalTime       time.Duration
}

// ImplementationResult contains results for a specific implementation
type ImplementationResult struct {
	Name              string
	Approach          IndexingApproach
	Success           bool
	Error             string
	BuildTime         time.Duration
	TotalSize         int64
	TotalRecords      uint32
	TotalUniqueValues uint32
	ColumnStats       map[string]*IndexStats
	OutputDirectory   string
	Features          []string
}

// PrintSummary prints a summary of the generation results
func (r *UnifiedGenerationResult) PrintSummary() {
	separator := "================================================================================"
	fmt.Println("\n" + separator)
	fmt.Println("📊 UNIFIED INDEX GENERATION SUMMARY")
	fmt.Println(separator)

	fmt.Printf("Table: %s\n", r.TableName)
	fmt.Printf("Total Records: %d\n", r.TotalRecords)
	fmt.Printf("Columns: %d\n", r.ColumnCount)
	fmt.Printf("Total Time: %v\n", r.TotalTime)
	fmt.Println()

	fmt.Println("Implementation Results:")
	fmt.Println("--------------------------------------------------------------------------------")

	for name, result := range r.Implementations {
		if result.Success {
			compressionRatio := float64(r.TotalRecords*50) / float64(result.TotalSize) // Rough estimate
			fmt.Printf("✅ %-20s | Build: %8v | Size: %8s | Compression: %.2fx\n",
				name,
				result.BuildTime.Round(time.Millisecond),
				formatBytes(result.TotalSize),
				compressionRatio)
		} else {
			fmt.Printf("❌ %-20s | Error: %s\n", name, result.Error)
		}
	}

	fmt.Println("\nOutput Directories:")
	fmt.Println("--------------------------------------------------------------------------------")
	for name, result := range r.Implementations {
		if result.Success {
			fmt.Printf("%-20s: %s\n", name, result.OutputDirectory)
		}
	}
}

// formatBytesUnified formats byte size in human readable format
func formatBytesUnified(bytes int64) string {
	const unit = 1024
	if bytes < unit {
		return fmt.Sprintf("%d B", bytes)
	}
	div, exp := int64(unit), 0
	for n := bytes / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(bytes)/float64(div), "KMGTPE"[exp])
}

// GetSuccessfulImplementations returns only the implementations that generated successfully
func (r *UnifiedGenerationResult) GetSuccessfulImplementations() map[string]*ImplementationResult {
	successful := make(map[string]*ImplementationResult)
	for name, result := range r.Implementations {
		if result.Success {
			successful[name] = result
		}
	}
	return successful
}

// GenerateUnifiedIndexes is a convenience function to generate indexes for all implementations
func GenerateUnifiedIndexes(csvFile, outputBase, tableName string) (*UnifiedGenerationResult, error) {
	generator := NewUnifiedDataGenerator(csvFile, outputBase, tableName)
	return generator.GenerateAllIndexes()
}
