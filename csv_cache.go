package main

import (
	"fmt"
	"io"
	"os"
	"strings"
	"sync"
)

// CSVCache provides efficient access to CSV data
type CSVCache struct {
	filename    string
	headers     []string
	data        [][]string
	columnIndex map[string]int
	mutex       sync.RWMutex
	loaded      bool
}

// NewCSVCache creates a new CSV cache
func NewCSVCache(filename string) *CSVCache {
	return &CSVCache{
		filename:    filename,
		columnIndex: make(map[string]int),
	}
}

// Load reads and caches the entire CSV file
func (c *CSVCache) Load() error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.loaded {
		return nil // Already loaded
	}

	file, err := os.Open(c.filename)
	if err != nil {
		return fmt.Errorf("failed to open CSV file: %v", err)
	}
	defer file.Close()

	// Read entire file
	fileInfo, err := file.Stat()
	if err != nil {
		return err
	}

	buffer := make([]byte, fileInfo.Size())
	_, err = file.Read(buffer)
	if err != nil && err != io.EOF {
		return err
	}

	lines := strings.Split(string(buffer), "\n")
	if len(lines) == 0 {
		return fmt.Errorf("empty CSV file")
	}

	// Parse header
	c.headers = strings.Split(lines[0], ",")
	for i, header := range c.headers {
		c.columnIndex[strings.TrimSpace(header)] = i
	}

	// Parse data rows
	c.data = make([][]string, 0, len(lines)-1)
	for i := 1; i < len(lines); i++ {
		line := strings.TrimSpace(lines[i])
		if line == "" {
			continue
		}
		fields := strings.Split(line, ",")
		c.data = append(c.data, fields)
	}

	c.loaded = true
	return nil
}

// GetValue returns the value for a specific line and column
func (c *CSVCache) GetValue(lineNumber uint32, columnName string) (string, error) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	if !c.loaded {
		return "", fmt.Errorf("CSV cache not loaded")
	}

	columnIndex, exists := c.columnIndex[columnName]
	if !exists {
		return "", fmt.Errorf("column %s not found", columnName)
	}

	// Convert line number to array index (line numbers are 1-based, arrays are 0-based)
	arrayIndex := int(lineNumber) - 1
	if arrayIndex < 0 || arrayIndex >= len(c.data) {
		return "", fmt.Errorf("line number %d out of range", lineNumber)
	}

	row := c.data[arrayIndex]
	if columnIndex >= len(row) {
		return "", fmt.Errorf("column %s not found in line %d", columnName, lineNumber)
	}

	return row[columnIndex], nil
}

// GetRow returns all values for a specific line
func (c *CSVCache) GetRow(lineNumber uint32) (map[string]string, error) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	if !c.loaded {
		return nil, fmt.Errorf("CSV cache not loaded")
	}

	// Convert line number to array index (line numbers are 1-based, arrays are 0-based)
	arrayIndex := int(lineNumber) - 1
	if arrayIndex < 0 || arrayIndex >= len(c.data) {
		return nil, fmt.Errorf("line number %d out of range", lineNumber)
	}

	row := c.data[arrayIndex]
	result := make(map[string]string)

	for columnName, columnIndex := range c.columnIndex {
		if columnIndex < len(row) {
			result[columnName] = row[columnIndex]
		} else {
			result[columnName] = ""
		}
	}

	return result, nil
}

// GetMultipleRows returns complete rows for multiple line numbers efficiently
func (c *CSVCache) GetMultipleRows(lineNumbers []uint32, selectColumns []string) ([]map[string]interface{}, error) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	if !c.loaded {
		return nil, fmt.Errorf("CSV cache not loaded")
	}

	if len(selectColumns) == 0 {
		// Default columns if none specified
		selectColumns = []string{"timestamp", "source_ip", "destination_ip", "destination_location", "message"}
	}

	results := make([]map[string]interface{}, 0, len(lineNumbers))

	for _, lineNumber := range lineNumbers {
		row := make(map[string]interface{})
		row["line_number"] = lineNumber

		// Convert line number to array index (line numbers are 1-based, arrays are 0-based)
		arrayIndex := int(lineNumber) - 1
		if arrayIndex < 0 || arrayIndex >= len(c.data) {
			// Skip invalid line numbers
			continue
		}

		dataRow := c.data[arrayIndex]

		// Get values for each selected column
		for _, columnName := range selectColumns {
			columnIndex, exists := c.columnIndex[columnName]
			if !exists || columnIndex >= len(dataRow) {
				row[columnName] = ""
			} else {
				row[columnName] = dataRow[columnIndex]
			}
		}

		results = append(results, row)
	}

	return results, nil
}

// GetHeaders returns the column headers
func (c *CSVCache) GetHeaders() []string {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	return c.headers
}

// GetRowCount returns the number of data rows (excluding header)
func (c *CSVCache) GetRowCount() int {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	return len(c.data)
}

// Global CSV cache instance
var globalCSVCache *CSVCache
var csvCacheOnce sync.Once

// GetGlobalCSVCache returns the global CSV cache instance
func GetGlobalCSVCache() *CSVCache {
	csvCacheOnce.Do(func() {
		globalCSVCache = NewCSVCache("mock_data.csv")
	})
	return globalCSVCache
}

// EnsureCSVCacheLoaded ensures the global CSV cache is loaded
func EnsureCSVCacheLoaded() error {
	cache := GetGlobalCSVCache()
	return cache.Load()
}
