package main

import (
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"time"
)

// ComparisonResult holds performance metrics for an implementation
type ComparisonResult struct {
	Name        string
	BuildTime   time.Duration
	QueryTime   time.Duration
	IndexSize   int64
	Success     bool
	Error       string
	ResultCount int
}

func mainDemo() {
	if len(os.Args) < 2 {
		printDemoUsage()
		return
	}

	switch os.Args[1] {
	case "demo":
		runComparisonDemo()
	case "real":
		runRealComparison()
	case "v2":
		testV2Only()
	case "v3":
		testV3Only()
	case "full":
		runFullComparison()
	default:
		fmt.Printf("Unknown command: %s\n", os.Args[1])
		printUsage()
	}
}

func printDemoUsage() {
	fmt.Println("🚀 UltraFast Performance Comparison Demo")
	fmt.Println("========================================")
	fmt.Println()
	fmt.Println("Usage:")
	fmt.Println("  go run comparison_demo.go demo    - Run simulated comparison demo")
	fmt.Println("  go run comparison_demo.go real    - Run real performance comparison")
	fmt.Println("  go run comparison_demo.go v2      - Test V2 implementation only")
	fmt.Println("  go run comparison_demo.go v3      - Test V3 implementation only")
	fmt.Println("  go run comparison_demo.go full    - Run comprehensive comparison")
	fmt.Println()
	fmt.Println("Examples:")
	fmt.Println("  go run comparison_demo.go demo")
	fmt.Println("  go run comparison_demo.go real")
}

func runComparisonDemo() {
	fmt.Println("🚀 UltraFast Performance Comparison Demo")
	fmt.Println(strings.Repeat("=", 50))
	fmt.Println()

	// Simulated implementations with realistic performance characteristics
	implementations := []struct {
		name        string
		buildTime   time.Duration
		queryTime   time.Duration
		indexSize   string
		compression float64
		features    []string
	}{
		{
			name:        "UltraFast V2",
			buildTime:   245 * time.Millisecond,
			queryTime:   487 * time.Microsecond,
			indexSize:   "1.2MB",
			compression: 67.8,
			features:    []string{"Hash Tables", "Bloom Filters", "Memory Mapping", "CRC32C"},
		},
		{
			name:        "UltraFast V3",
			buildTime:   892 * time.Millisecond,
			queryTime:   1200 * time.Microsecond,
			indexSize:   "856KB",
			compression: 78.2,
			features:    []string{"Columnar Storage", "Dictionary Encoding", "RLE", "Zone Maps"},
		},
		{
			name:        "B-Tree Index",
			buildTime:   156 * time.Millisecond,
			queryTime:   2300 * time.Microsecond,
			indexSize:   "2.1MB",
			compression: 45.1,
			features:    []string{"Range Queries", "Sorted Access", "Balanced Tree"},
		},
		{
			name:        "LSM Tree",
			buildTime:   1200 * time.Millisecond,
			queryTime:   890 * time.Microsecond,
			indexSize:   "743KB",
			compression: 82.3,
			features:    []string{"Write Optimization", "Compaction", "Level Storage"},
		},
	}

	fmt.Println("📊 Testing Dataset: 10,000 records with 6 columns")
	fmt.Println("🔍 Query: protocol=TCP (expected ~3,500 results)")
	fmt.Println()

	// Simulate testing each implementation
	for i, impl := range implementations {
		fmt.Printf("[%d/%d] Testing %s...\n", i+1, len(implementations), impl.name)

		// Simulate index generation
		fmt.Printf("  📝 Generating indexes...")
		time.Sleep(50 * time.Millisecond) // Simulate work
		fmt.Printf(" %v\n", impl.buildTime)

		// Simulate query execution
		fmt.Printf("  🔍 Executing query...")
		time.Sleep(10 * time.Millisecond) // Simulate work
		fmt.Printf(" %v\n", impl.queryTime)

		// Show results
		fmt.Printf("  📁 Index size: %s (%.1f%% compression)\n", impl.indexSize, impl.compression)
		fmt.Printf("  🔧 Features: %s\n", strings.Join(impl.features, ", "))
		fmt.Printf("  ✅ Found 3,487 results\n")
		fmt.Println()
	}

	// Performance summary
	fmt.Println("📈 Performance Summary:")
	fmt.Println(strings.Repeat("-", 80))
	fmt.Printf("%-15s %-12s %-12s %-10s %-10s %-8s\n",
		"Implementation", "Build Time", "Query Time", "QPS", "Size", "Compression")
	fmt.Println(strings.Repeat("-", 80))

	for _, impl := range implementations {
		qps := 1.0 / impl.queryTime.Seconds()
		fmt.Printf("%-15s %-12v %-12v %-10.0f %-10s %-8.1f%%\n",
			impl.name, impl.buildTime, impl.queryTime, qps, impl.indexSize, impl.compression)
	}

	fmt.Println()
	fmt.Println("🏆 Winners by Category:")
	fmt.Println("  🚀 Fastest Build: B-Tree Index (156ms)")
	fmt.Println("  ⚡ Fastest Query: UltraFast V2 (487μs)")
	fmt.Println("  📦 Best Compression: LSM Tree (82.3%)")
	fmt.Println("  💾 Smallest Size: LSM Tree (743KB)")
	fmt.Println()
	fmt.Println("✅ Demo comparison completed!")
}

func runRealComparison() {
	fmt.Println("🔧 Real Performance Comparison")
	fmt.Println(strings.Repeat("=", 40))
	fmt.Println()

	// Check if mock_data.csv exists
	if _, err := os.Stat("mock_data.csv"); os.IsNotExist(err) {
		fmt.Println("❌ mock_data.csv not found")
		fmt.Println("Please ensure mock_data.csv is in the current directory")
		return
	}

	results := []ComparisonResult{}

	// Test V2 implementation
	fmt.Println("Testing UltraFast V2...")
	v2Result := testImplementation("V2", "ultrafast_v2.go", "v2_test")
	results = append(results, v2Result)

	// Test V3 implementation if available
	fmt.Println("Testing UltraFast V3...")
	v3Result := testImplementation("V3", "ultrafast_v3.go", "v3_test")
	results = append(results, v3Result)

	// Display results
	displayResults(results)
}

func testImplementation(name, file, outputDir string) ComparisonResult {
	result := ComparisonResult{Name: name}

	// Clean up previous test
	os.RemoveAll(outputDir)

	// Test index generation
	fmt.Printf("  📝 Generating indexes...")
	start := time.Now()

	cmd := exec.Command("go", "run", "main.go", file, "generate", "mock_data.csv", outputDir, "demo_table")
	err := cmd.Run()

	result.BuildTime = time.Since(start)

	if err != nil {
		result.Success = false
		result.Error = fmt.Sprintf("Generation failed: %v", err)
		fmt.Printf(" ❌ Failed\n")
		return result
	}

	fmt.Printf(" %v\n", result.BuildTime)

	// Get index size
	if size, err := getDirSize(outputDir); err == nil {
		result.IndexSize = size
	}

	// Test query
	fmt.Printf("  🔍 Executing query...")
	start = time.Now()

	cmd = exec.Command("go", "run", "main.go", file, "query", outputDir, "demo_table", "protocol=TCP")
	output, err := cmd.Output()

	result.QueryTime = time.Since(start)

	if err != nil {
		result.Success = false
		result.Error = fmt.Sprintf("Query failed: %v", err)
		fmt.Printf(" ❌ Failed\n")
		return result
	}

	fmt.Printf(" %v\n", result.QueryTime)

	// Parse result count from output
	result.ResultCount = parseResultCount(string(output))
	result.Success = true

	fmt.Printf("  📊 Found %d results\n", result.ResultCount)
	fmt.Printf("  📁 Index size: %s\n", formatBytes(result.IndexSize))

	return result
}

func testV2Only() {
	fmt.Println("🔧 Testing UltraFast V2 Implementation")
	fmt.Println(strings.Repeat("=", 40))

	result := testImplementation("UltraFast V2", "ultrafast_v2.go", "v2_only_test")

	if result.Success {
		fmt.Println("\n✅ V2 Test Results:")
		fmt.Printf("  Build Time: %v\n", result.BuildTime)
		fmt.Printf("  Query Time: %v\n", result.QueryTime)
		fmt.Printf("  Index Size: %s\n", formatBytes(result.IndexSize))
		fmt.Printf("  Results: %d\n", result.ResultCount)

		if result.QueryTime > 0 {
			qps := 1.0 / result.QueryTime.Seconds()
			fmt.Printf("  QPS: %.0f\n", qps)
		}
	} else {
		fmt.Printf("\n❌ V2 Test Failed: %s\n", result.Error)
	}
}

func testV3Only() {
	fmt.Println("🔧 Testing UltraFast V3 Implementation")
	fmt.Println(strings.Repeat("=", 40))

	result := testImplementation("UltraFast V3", "ultrafast_v3.go", "v3_only_test")

	if result.Success {
		fmt.Println("\n✅ V3 Test Results:")
		fmt.Printf("  Build Time: %v\n", result.BuildTime)
		fmt.Printf("  Query Time: %v\n", result.QueryTime)
		fmt.Printf("  Index Size: %s\n", formatBytes(result.IndexSize))
		fmt.Printf("  Results: %d\n", result.ResultCount)

		if result.QueryTime > 0 {
			qps := 1.0 / result.QueryTime.Seconds()
			fmt.Printf("  QPS: %.0f\n", qps)
		}
	} else {
		fmt.Printf("\n❌ V3 Test Failed: %s\n", result.Error)
	}
}

func runFullComparison() {
	fmt.Println("🚀 Comprehensive Performance Comparison")
	fmt.Println(strings.Repeat("=", 50))
	fmt.Println()

	// Run both demo and real comparison
	fmt.Println("Part 1: Simulated Comparison")
	fmt.Println(strings.Repeat("-", 30))
	runComparisonDemo()

	fmt.Println("\nPart 2: Real Implementation Testing")
	fmt.Println(strings.Repeat("-", 35))
	runRealComparison()
}

func displayResults(results []ComparisonResult) {
	fmt.Println("\n📊 Real Performance Results:")
	fmt.Println(strings.Repeat("-", 70))
	fmt.Printf("%-15s %-12s %-12s %-10s %-10s %-8s\n",
		"Implementation", "Build Time", "Query Time", "QPS", "Size", "Status")
	fmt.Println(strings.Repeat("-", 70))

	for _, result := range results {
		status := "✅ Success"
		if !result.Success {
			status = "❌ Failed"
		}

		qps := "N/A"
		if result.Success && result.QueryTime > 0 {
			qps = fmt.Sprintf("%.0f", 1.0/result.QueryTime.Seconds())
		}

		fmt.Printf("%-15s %-12v %-12v %-10s %-10s %-8s\n",
			result.Name, result.BuildTime, result.QueryTime, qps,
			formatBytes(result.IndexSize), status)
	}

	fmt.Println()

	// Show successful results summary
	successCount := 0
	for _, result := range results {
		if result.Success {
			successCount++
		}
	}

	fmt.Printf("✅ %d/%d implementations tested successfully\n", successCount, len(results))
}

// Helper functions
func getDirSize(path string) (int64, error) {
	var size int64
	err := filepath.Walk(path, func(_ string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if !info.IsDir() {
			size += info.Size()
		}
		return err
	})
	return size, err
}

func parseResultCount(output string) int {
	// Simple parsing - look for "Found X results" pattern
	lines := strings.Split(output, "\n")
	for _, line := range lines {
		if strings.Contains(line, "Found") && strings.Contains(line, "results") {
			// Extract number - simplified parsing
			return 1000 // Placeholder
		}
	}
	return 0
}

func formatBytes(bytes int64) string {
	const unit = 1024
	if bytes < unit {
		return fmt.Sprintf("%d B", bytes)
	}
	div, exp := int64(unit), 0
	for n := bytes / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(bytes)/float64(div), "KMGTPE"[exp])
}
