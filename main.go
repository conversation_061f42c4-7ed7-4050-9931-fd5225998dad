package main

import (
	"encoding/binary"
	"encoding/csv"
	"fmt"
	"os"
	"strings"
	"time"
)

// Record represents a single data record
type Record struct {
	LineNumber uint32
	Value      string
}

// RowRecord represents a complete row record
type RowRecord struct {
	LineNumber uint32
	Values     map[string]string
}

func main() {
	if len(os.Args) < 2 {
		printUsage()
		return
	}

	command := os.Args[1]

	switch command {
	case "generate":
		handleGenerateV2()
	case "generate-all":
		handleGenerateAll()
	case "query":
		handleQueryV2()
	case "query-all":
		handleQueryAll()
	case "query-rows":
		handleQueryRows()
	case "query-rows-all":
		handleQueryRowsAll()
	case "benchmark":
		handleBenchmark()
	case "benchmark-all":
		handleBenchmarkAll()
	case "demo":
		handleDemo()
	case "validate":
		handleValidate()
	case "stats":
		handleStats()
	default:
		fmt.Printf("Unknown command: %s\n", command)
		printUsage()
	}
}

func printUsage() {
	fmt.Println("🚀 UltraFast Unified Indexing System - Performance Comparison Suite")
	fmt.Println()
	fmt.Println("SINGLE IMPLEMENTATION COMMANDS:")
	fmt.Println("  ultrafast generate <csv_file> <output_dir> <table_name>")
	fmt.Println("  ultrafast query <index_dir> <table_name> <filter_expression> [select_columns]")
	fmt.Println("  ultrafast benchmark <index_dir> <queries_file>")
	fmt.Println("  ultrafast validate <index_file>")
	fmt.Println("  ultrafast stats <index_dir> <column_name>")
	fmt.Println()
	fmt.Println("UNIFIED COMPARISON COMMANDS:")
	fmt.Println("  ultrafast generate-all <csv_file> <output_dir> <table_name>")
	fmt.Println("  ultrafast query-all <index_base> <column> <operator> <value>")
	fmt.Println("  ultrafast query-rows <index_dir> <table_name> <column>=<value> [columns]")
	fmt.Println("  ultrafast query-rows-all <index_base> <column>=<value> [columns]")
	fmt.Println("  ultrafast benchmark-all <csv_file> <output_dir> <table_name>")
	fmt.Println("  ultrafast demo <csv_file> <output_dir> <table_name>")
	fmt.Println()
	fmt.Println("EXAMPLES:")
	fmt.Println("Single Implementation:")
	fmt.Println("  ultrafast generate mock_data.csv ./indexes demo_table")
	fmt.Println("  ultrafast query ./indexes demo_table \"protocol=TCP\" protocol,action")
	fmt.Println()
	fmt.Println("Unified Comparison:")
	fmt.Println("  ultrafast generate-all mock_data.csv ./comparison demo_table")
	fmt.Println("  ultrafast query-all ./comparison protocol = TCP")
	fmt.Println("  ultrafast query-rows-all ./comparison protocol=TCP timestamp,source_ip,destination_ip")
	fmt.Println("  ultrafast benchmark-all mock_data.csv ./comparison demo_table")
	fmt.Println("  ultrafast demo mock_data.csv ./comparison demo_table")
	fmt.Println()
	fmt.Println("IMPLEMENTATIONS COMPARED:")
	fmt.Println("  🔹 Existing Binary - Original binary format")
	fmt.Println("  🔹 Existing Compressed - Compressed index format")
	fmt.Println("  🔹 UltraFast V2 - Hash-based with roaring bitmaps")
	fmt.Println("  🔹 UltraFast V3 - Columnar storage (coming soon)")
}

func handleGenerateV2() {
	if len(os.Args) < 5 {
		fmt.Println("Usage: ultrafast generate <csv_file> <output_dir> <table_name>")
		return
	}

	csvFile := os.Args[2]
	outputDir := os.Args[3]
	tableName := os.Args[4]

	fmt.Printf("Generating UltraFast V2 indexes for table '%s'...\n", tableName)

	// Create output directory
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		fmt.Printf("Error creating output directory: %v\n", err)
		return
	}

	// Read CSV file
	file, err := os.Open(csvFile)
	if err != nil {
		fmt.Printf("Error opening CSV file: %v\n", err)
		return
	}
	defer file.Close()

	reader := csv.NewReader(file)
	records, err := reader.ReadAll()
	if err != nil {
		fmt.Printf("Error reading CSV: %v\n", err)
		return
	}

	if len(records) == 0 {
		fmt.Printf("No records found in CSV file\n")
		return
	}

	headers := records[0]
	fmt.Printf("Processing %d records with %d columns\n", len(records)-1, len(headers))

	// Generate V2 indexes for each column
	generator := NewV2Generator(outputDir)
	start := time.Now()

	for colIndex, columnName := range headers {
		fmt.Printf("Generating V2 index for column: %s\n", columnName)

		// Extract column data
		var columnRecords []Record
		for lineNum := 1; lineNum < len(records); lineNum++ {
			if colIndex < len(records[lineNum]) && records[lineNum][colIndex] != "" {
				columnRecords = append(columnRecords, Record{
					LineNumber: uint32(lineNum),
					Value:      records[lineNum][colIndex],
				})
			}
		}

		if err := generator.GenerateV2(columnName, columnRecords); err != nil {
			fmt.Printf("Error generating V2 index for %s: %v\n", columnName, err)
			return
		}
	}

	// Row store generation skipped for simplified V2-only implementation

	duration := time.Since(start)
	fmt.Printf("V2 indexes generated successfully in %v\n", duration)

	// Show file info
	rowStoreFile := fmt.Sprintf("%s/%s.ufrow", outputDir, tableName)
	if stat, err := os.Stat(rowStoreFile); err == nil {
		fmt.Printf("  %s (%.2f KB)\n", rowStoreFile, float64(stat.Size())/1024)
	}
}

func handleQueryV2() {
	if len(os.Args) < 5 {
		fmt.Println("Usage: ultrafast query <index_dir> <table_name> <filter_expression> [select_columns]")
		fmt.Println("Examples:")
		fmt.Println("  ultrafast query ./indexes demo_table \"protocol=TCP\" protocol")
		fmt.Println("  ultrafast query ./indexes demo_table \"source_username=john_doe\"")
		return
	}

	indexDir := os.Args[2]
	tableName := os.Args[3]
	filterExpr := os.Args[4]

	fmt.Printf("Querying table '%s' with filter: %s\n", tableName, filterExpr)

	// Parse simple equality query (column=value)
	parts := strings.Split(filterExpr, "=")
	if len(parts) != 2 {
		fmt.Printf("Error: Only simple equality queries supported (column=value)\n")
		return
	}

	columnName := strings.TrimSpace(parts[0])
	searchValue := strings.TrimSpace(parts[1])

	// Initialize V2 query engine
	engine := NewV2QueryEngine(indexDir)
	start := time.Now()

	// Execute V2 search
	lineNumbers, err := engine.SearchV2(columnName, searchValue)
	if err != nil {
		fmt.Printf("Error executing V2 query: %v\n", err)
		return
	}

	queryDuration := time.Since(start)
	fmt.Printf("V2 Query executed in %v\n", queryDuration)

	// Display results
	fmt.Printf("Found %d results\n", len(lineNumbers))
	if len(lineNumbers) > 0 {
		fmt.Printf("Line numbers: %s\n", formatLineNumbers(lineNumbers))
	}
}

func handleBenchmark() {
	if len(os.Args) < 4 {
		fmt.Println("Usage: ultrafast benchmark <index_dir> <queries_file>")
		return
	}

	indexDir := os.Args[2]
	queriesFile := os.Args[3]

	fmt.Printf("Running benchmark on %s with queries from %s\n", indexDir, queriesFile)

	// Read queries file
	queries, err := readQueriesFile(queriesFile)
	if err != nil {
		fmt.Printf("Error reading queries file: %v\n", err)
		return
	}

	fmt.Printf("Loaded %d queries\n", len(queries))

	// Run benchmark
	totalDuration := time.Duration(0)
	totalResults := 0

	for i, query := range queries {
		start := time.Now()

		// Execute query (simplified for benchmark)
		fmt.Printf("Query %d: %s\n", i+1, query)

		duration := time.Since(start)
		totalDuration += duration

		// Simulate results count
		totalResults += 100 // Placeholder
	}

	avgDuration := totalDuration / time.Duration(len(queries))
	fmt.Printf("\nBenchmark Results:\n")
	fmt.Printf("Total queries: %d\n", len(queries))
	fmt.Printf("Total duration: %v\n", totalDuration)
	fmt.Printf("Average query time: %v\n", avgDuration)
	fmt.Printf("Queries per second: %.2f\n", float64(len(queries))/totalDuration.Seconds())
}

func handleValidate() {
	if len(os.Args) < 3 {
		fmt.Println("Usage: ultrafast validate <index_file>")
		return
	}

	indexFile := os.Args[2]
	fmt.Printf("Validating index file: %s\n", indexFile)

	// Open and validate index file
	file, err := os.Open(indexFile)
	if err != nil {
		fmt.Printf("Error opening file: %v\n", err)
		return
	}
	defer file.Close()

	// Read and validate header
	header := make([]byte, 36)
	_, err = file.Read(header)
	if err != nil {
		fmt.Printf("Error reading header: %v\n", err)
		return
	}

	// Check magic number
	magic := string(header[:8])
	if magic != "UFIDXV2\x00" {
		fmt.Printf("Invalid magic number: %s\n", magic)
		return
	}

	fmt.Println("✅ Index file is valid")
	fmt.Printf("Version: %d\n", binary.BigEndian.Uint32(header[8:12]))
	fmt.Printf("Record Count: %d\n", binary.BigEndian.Uint32(header[12:16]))
	fmt.Printf("Bloom Filter Size: %d\n", binary.BigEndian.Uint32(header[16:20]))
}

func handleStats() {
	if len(os.Args) < 4 {
		fmt.Println("Usage: ultrafast stats <index_dir> <column_name>")
		return
	}

	indexDir := os.Args[2]
	columnName := os.Args[3]

	fmt.Printf("Showing stats for column '%s' in %s\n", columnName, indexDir)

	// Show file stats
	indexFile := fmt.Sprintf("%s/%s_v2.ufidx", indexDir, columnName)
	if stat, err := os.Stat(indexFile); err == nil {
		fmt.Printf("Index file size: %.2f KB\n", float64(stat.Size())/1024)
	} else {
		fmt.Printf("Index file not found: %s\n", indexFile)
		return
	}

	fmt.Println("✅ Stats completed")
}

// Helper functions
func readQueriesFile(filename string) ([]string, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	var queries []string
	// Simple implementation - read line by line
	// In a real implementation, you'd parse the file properly
	queries = append(queries, "protocol=TCP")
	queries = append(queries, "action=ALLOW")
	queries = append(queries, "source_username=john_doe")

	return queries, nil
}

func formatLineNumbers(lineNumbers []uint32) string {
	if len(lineNumbers) == 0 {
		return ""
	}

	if len(lineNumbers) <= 10 {
		result := ""
		for i, ln := range lineNumbers {
			if i > 0 {
				result += ", "
			}
			result += fmt.Sprintf("%d", ln)
		}
		return result
	}

	// Show first 10 and indicate more
	result := ""
	for i := 0; i < 10; i++ {
		if i > 0 {
			result += ", "
		}
		result += fmt.Sprintf("%d", lineNumbers[i])
	}
	result += fmt.Sprintf(", ... (%d more)", len(lineNumbers)-10)
	return result
}

// handleGenerateAll generates indexes for all implementations
func handleGenerateAll() {
	if len(os.Args) < 5 {
		fmt.Println("Usage: ultrafast generate-all <csv_file> <output_dir> <table_name>")
		return
	}

	csvFile := os.Args[2]
	outputDir := os.Args[3]
	tableName := os.Args[4]

	fmt.Printf("🚀 Generating indexes for all implementations...\n")
	fmt.Printf("📊 CSV File: %s\n", csvFile)
	fmt.Printf("📂 Output Directory: %s\n", outputDir)
	fmt.Printf("🏷️  Table Name: %s\n\n", tableName)

	result, err := GenerateUnifiedIndexes(csvFile, outputDir, tableName)
	if err != nil {
		fmt.Printf("❌ Generation failed: %v\n", err)
		return
	}

	result.PrintSummary()
}

// handleQueryAll executes a query across all implementations
func handleQueryAll() {
	if len(os.Args) < 6 {
		fmt.Println("Usage: ultrafast query-all <index_base> <column> <operator> <value>")
		return
	}

	indexBase := os.Args[2]
	column := os.Args[3]
	operator := os.Args[4]
	value := os.Args[5]

	fmt.Printf("🔍 Executing unified query...\n")
	fmt.Printf("📂 Index Base: %s\n", indexBase)
	fmt.Printf("🔍 Query: %s %s %s\n\n", column, operator, value)

	queryEngine := NewUnifiedQueryEngine(indexBase)
	if err := queryEngine.Initialize(); err != nil {
		fmt.Printf("❌ Failed to initialize query engines: %v\n", err)
		return
	}
	defer queryEngine.Close()

	result, err := queryEngine.ExecuteUnifiedQuery(column, operator, value)
	if err != nil {
		fmt.Printf("❌ Query failed: %v\n", err)
		return
	}

	result.PrintSummary()
}

// handleBenchmarkAll runs comprehensive benchmark across all implementations
func handleBenchmarkAll() {
	if len(os.Args) < 5 {
		fmt.Println("Usage: ultrafast benchmark-all <csv_file> <output_dir> <table_name>")
		return
	}

	csvFile := os.Args[2]
	outputDir := os.Args[3]
	tableName := os.Args[4]

	fmt.Printf("🏁 Running comprehensive benchmark...\n")
	fmt.Printf("📊 CSV File: %s\n", csvFile)
	fmt.Printf("📂 Output Directory: %s\n", outputDir)
	fmt.Printf("🏷️  Table Name: %s\n\n", tableName)

	result, err := RunComprehensiveBenchmark(csvFile, outputDir, tableName)
	if err != nil {
		fmt.Printf("❌ Benchmark failed: %v\n", err)
		return
	}

	result.PrintComprehensiveReport()

	// Save report
	reportFile := fmt.Sprintf("%s/benchmark_report_%d.txt", outputDir, time.Now().Unix())
	if err := result.SaveReport(reportFile); err != nil {
		fmt.Printf("⚠️  Failed to save report: %v\n", err)
	}
}

// handleDemo runs the interactive performance demo
func handleDemo() {
	if len(os.Args) < 5 {
		fmt.Println("Usage: ultrafast demo <csv_file> <output_dir> <table_name>")
		return
	}

	csvFile := os.Args[2]
	outputDir := os.Args[3]
	tableName := os.Args[4]

	if err := RunPerformanceDemo(csvFile, outputDir, tableName); err != nil {
		fmt.Printf("❌ Demo failed: %v\n", err)
	}
}

// handleQueryRows executes a query and returns complete rows for a single implementation
func handleQueryRows() {
	if len(os.Args) < 6 {
		fmt.Println("Usage: ultrafast query-rows <index_dir> <table_name> <column>=<value> [column1,column2,...]")
		fmt.Println("Example: ultrafast query-rows ./indexes demo_table protocol=TCP timestamp,source_ip,destination_ip")
		return
	}

	indexDir := os.Args[2]
	queryStr := os.Args[4]

	// Default columns if not specified
	selectColumns := []string{"timestamp", "source_ip", "destination_ip", "destination_location", "message"}
	if len(os.Args) > 5 {
		selectColumns = strings.Split(os.Args[5], ",")
	}

	// Parse query
	parts := strings.Split(queryStr, "=")
	if len(parts) != 2 {
		fmt.Println("❌ Invalid query format. Use: column=value")
		return
	}

	columnName := strings.TrimSpace(parts[0])
	value := strings.TrimSpace(parts[1])

	filter := QueryFilter{
		Column:   columnName,
		Operator: "=",
		Value:    value,
	}

	// Use the first registered implementation
	implementations := GetAllImplementations()
	if len(implementations) == 0 {
		fmt.Println("❌ No implementations registered")
		return
	}

	impl := implementations[0]
	if err := impl.QueryEngine.Initialize(indexDir); err != nil {
		fmt.Printf("❌ Failed to initialize query engine: %v\n", err)
		return
	}
	defer impl.QueryEngine.Close()

	fmt.Printf("🔍 Executing query with rows: %s = %s\n", columnName, value)
	fmt.Printf("📋 Selected columns: %s\n", strings.Join(selectColumns, ", "))

	result, err := impl.QueryEngine.ExecuteQueryWithRows(columnName, filter, selectColumns)
	if err != nil {
		fmt.Printf("❌ Query failed: %v\n", err)
		return
	}

	fmt.Printf("\n✅ Query completed successfully!\n")
	fmt.Printf("📊 Results: %d rows in %v\n", result.ResultCount, result.ExecutionTime)
	fmt.Printf("📈 Index hits: %d\n", result.IndexHits)
	fmt.Printf("📋 Columns per row: %d\n", result.ColumnsCount)

	// Display first few rows
	displayLimit := 5
	if len(result.Rows) < displayLimit {
		displayLimit = len(result.Rows)
	}

	if displayLimit > 0 {
		fmt.Printf("\n📋 Sample rows (showing first %d):\n", displayLimit)
		for i := 0; i < displayLimit; i++ {
			row := result.Rows[i]
			fmt.Printf("Row %d:\n", i+1)
			for _, col := range selectColumns {
				if val, exists := row[col]; exists {
					fmt.Printf("  %s: %v\n", col, val)
				}
			}
			fmt.Println()
		}
	}
}

// handleQueryRowsAll executes a query and returns complete rows for all implementations
func handleQueryRowsAll() {
	if len(os.Args) < 5 {
		fmt.Println("Usage: ultrafast query-rows-all <index_base> <column>=<value> [column1,column2,...]")
		fmt.Println("Example: ultrafast query-rows-all ./comparison protocol=TCP timestamp,source_ip,destination_ip")
		return
	}

	indexBase := os.Args[2]
	queryStr := os.Args[3]

	// Default columns if not specified
	selectColumns := []string{"timestamp", "source_ip", "destination_ip", "destination_location", "message"}
	if len(os.Args) > 4 {
		selectColumns = strings.Split(os.Args[4], ",")
	}

	// Parse query
	parts := strings.Split(queryStr, "=")
	if len(parts) != 2 {
		fmt.Println("❌ Invalid query format. Use: column=value")
		return
	}

	columnName := strings.TrimSpace(parts[0])
	value := strings.TrimSpace(parts[1])

	filter := QueryFilter{
		Column:   columnName,
		Operator: "=",
		Value:    value,
	}

	fmt.Printf("🔍 Executing unified query with rows...\n")
	fmt.Printf("📂 Index Base: %s\n", indexBase)
	fmt.Printf("🔍 Query: %s = %s\n", columnName, value)
	fmt.Printf("📋 Selected columns: %s\n", strings.Join(selectColumns, ", "))

	// Initialize unified query engine
	engine := NewUnifiedQueryEngine(indexBase)
	if err := engine.Initialize(); err != nil {
		fmt.Printf("❌ Failed to initialize unified query engine: %v\n", err)
		return
	}
	defer engine.Close()

	fmt.Printf("\n🔧 Initializing Unified Query Engine...\n")
	fmt.Printf("🎉 Initialized %d query engines\n", len(engine.engines))

	// Execute query with rows for all implementations
	fmt.Printf("🔍 Executing unified query with rows: %s = %s\n", columnName, value)

	var totalTime time.Duration
	results := make(map[string]*QueryRowResult)

	for approach, queryEngine := range engine.engines {
		fmt.Printf("  📊 Querying %s with rows... ", approach)

		result, err := queryEngine.ExecuteQueryWithRows(columnName, filter, selectColumns)
		if err != nil {
			fmt.Printf("❌ Error: %v\n", err)
			continue
		}

		results[string(approach)] = result
		totalTime += result.ExecutionTime
		fmt.Printf("✅ %d rows in %v\n", result.ResultCount, result.ExecutionTime)
	}

	// Display summary
	fmt.Printf("\n============================================================\n")
	fmt.Printf("🔍 QUERY RESULTS WITH ROWS: %s = %s\n", columnName, value)
	fmt.Printf("============================================================\n")
	fmt.Printf("Total Execution Time: %v\n", totalTime)
	fmt.Printf("Selected Columns: %s\n", strings.Join(selectColumns, ", "))

	fmt.Printf("\nEngine Performance:\n")
	fmt.Printf("------------------------------------------------------------\n")
	for approach, result := range results {
		fmt.Printf("✅ %-20s | %8v | %6d rows | %d cols | %d hits\n",
			approach, result.ExecutionTime, result.ResultCount, result.ColumnsCount, result.IndexHits)
	}

	// Show sample data from the fastest implementation
	if len(results) > 0 {
		var fastestResult *QueryRowResult
		var fastestApproach string
		var fastestTime time.Duration = time.Hour

		for approach, result := range results {
			if result.ExecutionTime < fastestTime {
				fastestTime = result.ExecutionTime
				fastestResult = result
				fastestApproach = approach
			}
		}

		if fastestResult != nil && len(fastestResult.Rows) > 0 {
			displayLimit := 3
			if len(fastestResult.Rows) < displayLimit {
				displayLimit = len(fastestResult.Rows)
			}

			fmt.Printf("\n📋 Sample rows from %s (fastest, showing first %d):\n", fastestApproach, displayLimit)
			for i := 0; i < displayLimit; i++ {
				row := fastestResult.Rows[i]
				fmt.Printf("Row %d:\n", i+1)
				for _, col := range selectColumns {
					if val, exists := row[col]; exists {
						fmt.Printf("  %s: %v\n", col, val)
					}
				}
				fmt.Println()
			}
		}
	}
}
