#!/bin/bash

# UltraFast Performance Comparison Script
# Usage: ./compare.sh [demo|real|v2|quick]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_header() {
    echo -e "${BLUE}🚀 $1${NC}"
    echo -e "${BLUE}$(printf '=%.0s' $(seq 1 ${#1}))${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${CYAN}ℹ️  $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Function to check if file exists
check_file() {
    if [ ! -f "$1" ]; then
        print_error "File $1 not found"
        return 1
    fi
    return 0
}

# Function to run demo comparison
run_demo() {
    print_header "UltraFast Performance Comparison Demo"
    echo
    
    print_info "Running simulated performance comparison..."
    go run comparison_demo.go demo
}

# Function to run real comparison
run_real() {
    print_header "Real Performance Comparison"
    echo
    
    # Check prerequisites
    if ! check_file "mock_data.csv"; then
        print_warning "mock_data.csv not found. Creating sample data..."
        create_sample_data
    fi
    
    if ! check_file "main.go"; then
        print_error "main.go not found"
        exit 1
    fi
    
    print_info "Running real performance comparison..."
    go run comparison_demo.go real
}

# Function to test V2 only
test_v2() {
    print_header "Testing UltraFast V2 Implementation"
    echo
    
    if ! check_file "ultrafast_v2.go"; then
        print_error "ultrafast_v2.go not found"
        exit 1
    fi
    
    if ! check_file "mock_data.csv"; then
        print_warning "mock_data.csv not found. Creating sample data..."
        create_sample_data
    fi
    
    print_info "Testing V2 implementation..."
    go run comparison_demo.go v2
}

# Function to create sample data if mock_data.csv doesn't exist
create_sample_data() {
    print_info "Creating sample mock_data.csv..."
    
    cat > mock_data.csv << 'EOF'
protocol,rule_name,source_ip,dest_ip,action,source_username
TCP,ALLOW_WEB,************,********,ALLOW,john_doe
UDP,ALLOW_DNS,************,*******,ALLOW,alice
TCP,ALLOW_SSH,************,********,ALLOW,bob
HTTP,ALLOW_WEB,************,********,ALLOW,charlie
TCP,BLOCK_MALWARE,************,suspicious.com,BLOCK,system
UDP,ALLOW_DNS,************,*******,ALLOW,dave
HTTPS,ALLOW_WEB,************,********,ALLOW,eve
TCP,ALLOW_SSH,************,********,ALLOW,admin
TCP,ALLOW_WEB,************,********,ALLOW,guest
UDP,BLOCK_P2P,************,peer.network,BLOCK,user1
EOF
    
    print_success "Sample data created"
}

# Function to run quick benchmark
quick_benchmark() {
    print_header "Quick Performance Benchmark"
    echo
    
    if ! check_file "mock_data.csv"; then
        create_sample_data
    fi
    
    print_info "Running quick V2 benchmark..."
    
    # Clean up previous test
    rm -rf ./quick_test
    
    # Test V2 generation
    echo -n "📝 Index generation: "
    start_time=$(date +%s%N)
    if go run main.go ultrafast_v2.go generate mock_data.csv ./quick_test demo_table >/dev/null 2>&1; then
        end_time=$(date +%s%N)
        build_time=$(( (end_time - start_time) / 1000000 ))
        echo "${build_time}ms"
        
        # Test V2 query
        echo -n "🔍 Query execution: "
        start_time=$(date +%s%N)
        if result=$(go run main.go ultrafast_v2.go query ./quick_test demo_table "protocol=TCP" 2>/dev/null); then
            end_time=$(date +%s%N)
            query_time=$(( (end_time - start_time) / 1000000 ))
            echo "${query_time}ms"
            
            # Get index size
            index_size=$(du -sh ./quick_test 2>/dev/null | cut -f1)
            echo "📁 Index size: $index_size"
            
            # Parse results
            if echo "$result" | grep -q "Found"; then
                result_count=$(echo "$result" | grep "Found" | head -1)
                echo "📊 $result_count"
            fi
            
            print_success "Quick benchmark completed"
        else
            print_error "Query failed"
        fi
    else
        print_error "Index generation failed"
    fi
    
    # Clean up
    rm -rf ./quick_test
}

# Function to show usage
show_usage() {
    echo -e "${PURPLE}🚀 UltraFast Performance Comparison Script${NC}"
    echo
    echo "Usage: $0 [command]"
    echo
    echo "Commands:"
    echo "  demo     - Run simulated performance comparison demo"
    echo "  real     - Run real performance comparison with existing implementations"
    echo "  v2       - Test UltraFast V2 implementation only"
    echo "  quick    - Run quick benchmark with V2"
    echo "  help     - Show this help message"
    echo
    echo "Examples:"
    echo "  $0 demo          # Run simulated demo"
    echo "  $0 real          # Test real implementations"
    echo "  $0 quick         # Quick V2 benchmark"
    echo
    echo "Prerequisites:"
    echo "  - Go installed and available in PATH"
    echo "  - mock_data.csv (will be created if missing)"
    echo "  - main.go, ultrafast_v2.go (for real tests)"
}

# Main script logic
case "${1:-help}" in
    "demo")
        run_demo
        ;;
    "real")
        run_real
        ;;
    "v2")
        test_v2
        ;;
    "quick")
        quick_benchmark
        ;;
    "help"|"--help"|"-h")
        show_usage
        ;;
    *)
        print_error "Unknown command: $1"
        echo
        show_usage
        exit 1
        ;;
esac
